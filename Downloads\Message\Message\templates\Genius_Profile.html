<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="GigGenius - Professional freelancer profile and portfolio">
    <title>GigGenius - Profile</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-blue: #2563eb;
            --primary-pink: #d41b8c;
            --text-light: #ffffff;
            --text-dark: #333333;
            --gray-100: #f8f9fa;
            --gray-200: #e9ecef;
            --gray-300: #dee2e6;
            --gray-400: #ced4da;
            --gray-500: #adb5bd;
            --gray-600: #6c757d;
            --gray-700: #495057;
            --gray-800: #343a40;
            --border-color: #e5e7eb;
            --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
            --border-radius-sm: 4px;
            --border-radius: 8px;
            --border-radius-lg: 12px;
            --border-radius-xl: 20px;
            --transition-fast: 0.2s ease;
            --transition: 0.3s ease;
        }

        /* Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            background-color: var(--gray-100);
            color: var(--text-dark);
            line-height: 1.5;
        }

        a {
            text-decoration: none;
            color: inherit;
        }

        ul {
            list-style: none;
        }

        button, input, select, textarea {
            font-family: inherit;
            font-size: inherit;
            outline: none;
        }

        button {
            cursor: pointer;
            border: none;
            background: none;
        }

        img, video {
            max-width: 100%;
            height: auto;
        }

        /* Layout */
        .container {
            max-width: 2000px;
            margin: 0 auto;
            background-color: white;
            box-shadow: var(--shadow-sm);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .main-content {
            padding: 1.5rem 1rem 3rem;
            flex: 1;
        }

        /* Header Styles */
        .header {
            position: sticky;
            top: 0;
            z-index: 10;
            background-color: white;
            border-bottom: 1px solid #e5e7eb;
        }

        .header-content {
            display: flex;
            align-items: center;
            height: 64px;
            padding: 0 1.5rem;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-right: 2rem;
        }

        .logo-image {
            width: 2.5rem;
            height: 2.5rem;
            border-radius: 50%;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .logo-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .logo-text {
            font-size: 1.25rem;
            font-weight: bold;
            background: linear-gradient(135deg, #8b5cf6, #d946ef);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-links {
            display: none;
            align-items: center;
            gap: 2rem;
            flex: 1;
        }

        @media (min-width: 1024px) {
            .nav-links {
                display: flex;
            }
        }

        .nav-links a {
            font-size: 0.875rem;
            font-weight: 500;
            color: var(--primary-blue);
            transition: color 0.2s;
            white-space: nowrap;
        }

        .nav-links a:hover {
            color: #1d4ed8;
        }

        /* Dropdown styles */
        .dropdown {
            position: relative;
            display: inline-block;
        }

        .dropdown-trigger {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            cursor: pointer;
            font-size: 0.875rem;
            font-weight: 500;
            color: var(--primary-blue);
        }

        .dropdown-trigger i {
            font-size: 0.75rem;
            transition: transform 0.2s;
        }

        .dropdown:hover .dropdown-trigger i {
            transform: rotate(180deg);
        }

        .dropdown-menu {
            position: absolute;
            top: 100%;
            left: 0;
            background-color: white;
            min-width: 180px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            border-radius: 4px;
            padding: 0.5rem 0;
            z-index: 100;
            display: none;
        }

        .dropdown:hover .dropdown-menu {
            display: block;
        }

        .dropdown-menu a {
            display: block;
            padding: 0.5rem 1rem;
            margin: 0;
            color: var(--primary-blue);
            font-size: 0.875rem;
        }

        .dropdown-menu a:hover {
            background-color: #f5f5f5;
            color: #1d4ed8;
        }

        /* Header actions styles */
        .header-actions {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-left: auto;
        }

        .gigs-btn {
            display: none;
            padding: 0.5rem 1rem;
            border: 1px solid var(--primary-blue);
            color: var(--primary-blue);
            background: white;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }

        @media (min-width: 768px) {
            .gigs-btn {
                display: block;
            }
        }

        .gigs-btn:hover {
            background-color: #eff6ff;
        }

        .search-wrapper {
            position: relative;
            display: none;
        }

        @media (min-width: 768px) {
            .search-wrapper {
                display: block;
            }
        }

        .header-search {
            padding: 0.5rem 1rem;
            padding-right: 2.5rem;
            border: 1px solid var(--primary-blue);
            border-radius: 6px;
            font-size: 0.875rem;
            width: 256px;
        }

        .header-search:focus {
            outline: none;
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1);
        }

        .search-wrapper i {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--primary-blue);
            font-size: 0.875rem;
        }

        .profile-dropdown {
            position: relative;
        }

        .profile-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            overflow: hidden;
            display: none;
            cursor: pointer;
            border: 2px solid var(--primary-blue);
        }

        @media (min-width: 768px) {
            .profile-icon {
                display: block;
            }
        }

        .profile-icon img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .profile-dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background-color: #000;
            min-width: 180px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.3);
            border-radius: 4px;
            padding: 0.5rem 0;
            z-index: 100;
            display: none;
            margin-top: 0.5rem;
        }

        .profile-dropdown-menu a {
            display: block;
            padding: 0.75rem 1rem;
            color: #fff;
            font-size: 0.875rem;
            transition: background-color 0.2s;
            text-decoration: none;
        }

        .profile-dropdown-menu a:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: var(--primary-blue);
        }

        .profile-dropdown-menu.active {
            display: block;
        }

        .notification-btn {
            position: relative;
            background: none;
            border: none;
            cursor: pointer;
            width: 40px;
            height: 40px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-blue);
        }

        .notification-btn:hover {
            background-color: #eff6ff;
        }

        .notification-badge {
            position: absolute;
            -top: 4px;
            -right: 4px;
            width: 20px;
            height: 20px;
            background-color: var(--primary-pink);
            color: white;
            border-radius: 50%;
            font-size: 0.75rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Notification Dropdown Styles */
        .notification-dropdown {
            position: relative;
        }

        .notification-dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background-color: white;
            min-width: 320px;
            max-height: 400px;
            overflow-y: auto;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            border-radius: 8px;
            padding: 0;
            z-index: 100;
            display: none;
            margin-top: 0.5rem;
            border: 1px solid #e5e7eb;
        }

        .notification-dropdown-menu.active {
            display: block;
        }

        .notification-header {
            padding: 1rem;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .notification-header h3 {
            margin: 0;
            font-size: 1rem;
            font-weight: 600;
            color: #333;
        }

        .notification-header .mark-all-read {
            font-size: 0.75rem;
            color: #064dac;
            cursor: pointer;
            background: none;
            border: none;
            padding: 0;
        }

        .notification-header .mark-all-read:hover {
            text-decoration: underline;
        }

        .notification-list {
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .notification-item {
            padding: 0.75rem 1rem;
            border-bottom: 1px solid #f3f4f6;
            cursor: pointer;
            transition: background-color 0.2s;
            display: flex;
            align-items: flex-start;
            gap: 0.75rem;
        }

        .notification-item:hover {
            background-color: #f9fafb;
        }

        .notification-item:last-child {
            border-bottom: none;
        }

        .notification-item.unread {
            background-color: #f0f7ff;
        }

        .notification-item.unread:hover {
            background-color: #e1f0ff;
        }

        .notification-icon {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background-color: #e5e7eb;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        .notification-icon i {
            color: #064dac;
            font-size: 1rem;
        }

        .notification-content {
            flex: 1;
        }

        .notification-text {
            font-size: 0.875rem;
            color: #333;
            margin-bottom: 0.25rem;
            line-height: 1.4;
        }

        .notification-text strong {
            color: #003a8c;
            font-weight: 600;
        }

        .notification-time {
            font-size: 0.75rem;
            color: #6b7280;
        }

        .notification-footer {
            padding: 0.75rem 1rem;
            text-align: center;
            border-top: 1px solid #e5e7eb;
        }

        .notification-footer a {
            font-size: 0.875rem;
            color: #064dac;
            text-decoration: none;
        }

        .notification-footer a:hover {
            text-decoration: underline;
        }

        .no-notifications {
            padding: 2rem 1rem;
            text-align: center;
            color: #6b7280;
            font-size: 0.875rem;
        }

        .mobile-menu-btn {
            background: none;
            border: none;
            cursor: pointer;
            width: 40px;
            height: 40px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-blue);
        }

        @media (min-width: 1024px) {
            .mobile-menu-btn {
                display: none;
            }
        }

        .mobile-menu-btn:hover {
            background-color: #eff6ff;
        }

        .mobile-menu {
            display: none;
            position: absolute;
            top: 60px;
            right: 1rem;
            background-color: white;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            width: 200px;
            z-index: 20;
            border: 1px solid #e5e7eb;
        }

        @media (min-width: 768px) {
            .mobile-menu {
                display: none !important;
            }
        }

        .mobile-menu.active {
            display: block;
        }

        .mobile-menu a {
            display: block;
            padding: 0.75rem 1rem;
            color: #333;
            font-size: 0.875rem;
            border-bottom: 1px solid #f3f4f6;
        }

        .mobile-menu a:last-child {
            border-bottom: none;
        }

        .mobile-menu a:hover {
            background-color: #f9fafb;
        }

        /* Profile Section */
        .profile-section {
            margin-bottom: 2rem;
        }

        .profile-header {
            margin-bottom: 1rem;
        }

        .profile-name {
            font-size: 1.875rem;
            font-weight: bold;
            color: var(--primary-blue);
            margin-bottom: 0.5rem;
        }

        .profile-title {
            font-size: 1.125rem;
            color: var(--gray-600);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .profile-title i {
            color: var(--primary-pink);
        }

        .profile-row {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        @media (min-width: 1024px) {
            .profile-row {
                flex-direction: row;
            }
        }

        .video-card, .profile-card {
            background-color: white;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
            width: 100%;
            transition: transform 0.3s, box-shadow 0.3s;
        }

        .video-card:hover, .profile-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        @media (min-width: 1024px) {
            .video-card, .profile-card {
                width: 50%;
            }
        }

        /* Video Container - Centered */
        .video-container {
            position: relative;
            width: 100%;
            height: 0;
            padding-bottom: 56.25%; /* 16:9 Aspect Ratio */
            overflow: hidden;
            background-color: #000;
        }

        .video-container video {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .expand-btn {
            position: absolute;
            bottom: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.5);
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 12px;
            cursor: pointer;
            z-index: 2;
            transition: background-color 0.3s;
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 0.875rem;
        }

        .expand-btn:hover {
            background: rgba(0, 0, 0, 0.7);
        }

        .video-container.expanded {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            padding: 0;
            background: rgba(0, 0, 0, 0.9);
            z-index: 1000;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .video-container.expanded video {
            width: 90%;
            height: 90%;
            object-fit: contain;
        }

        /* Profile Content */
        .profile-content {
            padding: 1.5rem;
        }

        .profile-stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        @media (min-width: 768px) {
            .profile-stats {
                grid-template-columns: repeat(4, 1fr);
            }
        }

        .stat-item {
            text-align: left;
        }

        .stat-value {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--primary-pink);
        }

        .stat-label {
            font-size: 0.875rem;
            color: var(--gray-600);
        }

        .profile-summary {
            margin-bottom: 1.5rem;
        }

        .profile-summary h3 {
            font-size: 1.125rem;
            font-weight: 600;
            margin-bottom: 0.75rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .profile-summary h3 i {
            color: var(--primary-blue);
        }

        .summary-text {
            color: var(--gray-600);
            margin-bottom: 0.5rem;
        }

        .show-more {
            color: var(--primary-pink);
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 0.375rem;
        }

        .show-more:hover {
            text-decoration: underline;
        }

        .edit-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.375rem;
            color: var(--primary-pink);
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            margin-left: 0.5rem;
            background: none;
            border: none;
        }

        .edit-btn:hover {
            text-decoration: underline;
        }

        .profile-fields {
            margin-bottom: 1.5rem;
        }

        .field-group {
            margin-bottom: 1rem;
        }

        .field-group label {
            display: block;
            font-size: 0.875rem;
            font-weight: 500;
            margin-bottom: 0.375rem;
            color: var(--gray-700);
        }

        .field-input {
            width: 100%;
            padding: 0.625rem;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            font-size: 0.875rem;
            background-color: #f9fafb;
            color: #333;
        }

        .edit-profile-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.625rem 1.25rem;
            background-color: var(--primary-pink);
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
            width: 100%;
            justify-content: center;
        }

        .edit-profile-btn:hover {
            background-color: #b91c77;
        }

        /* Portfolio and Work History Section */
        .portfolio-section {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .section-row {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        @media (min-width: 1024px) {
            .section-row {
                flex-direction: row;
            }
        }

        .section-card {
            background-color: white;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
            width: 100%;
            transition: transform 0.3s, box-shadow 0.3s;
        }

        .section-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        @media (min-width: 1024px) {
            .section-card {
                width: 50%;
            }
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1.25rem;
            border-bottom: 1px solid #e5e7eb;
        }

        .section-title {
            font-size: 1.125rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .section-title i {
            color: var(--primary-blue);
        }

        .section-content {
            padding: 1.25rem;
        }

        .introduction-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .textarea {
            width: 100%;
            min-height: 150px;
            padding: 0.75rem;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            font-size: 0.875rem;
            resize: vertical;
            margin-bottom: 1.25rem;
            background-color: #f9fafb;
        }

        .section-actions {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .arrow-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            border-radius: 6px;
            background-color: #f3f4f6;
            color: #6b7280;
            cursor: pointer;
            transition: all 0.2s;
        }

        .arrow-btn:hover {
            background-color: #e5e7eb;
            color: #333;
        }

        .add-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.375rem;
            padding: 0.5rem 0.75rem;
            background-color: var(--primary-pink);
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .add-btn:hover {
            background-color: #b91c77;
        }

        .portfolio-image {
            width: 100%;
            height: 250px;
            border-radius: 6px;
            overflow: hidden;
            margin-bottom: 1.25rem;
            background-color: #f3f4f6;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6b7280;
        }

        .portfolio-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s;
        }

        .portfolio-image:hover img {
            transform: scale(1.05);
        }

        /* New Portfolio Design */
        .portfolio-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding: 0 1.25rem;
        }

        .portfolio-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-dark);
            margin: 0;
        }

        .portfolio-actions {
            display: flex;
            gap: 0.5rem;
        }

        .portfolio-add-btn, .portfolio-refresh-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 2px solid var(--primary-blue);
            background: white;
            color: var(--primary-blue);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s;
        }

        .portfolio-add-btn:hover, .portfolio-refresh-btn:hover {
            background: var(--primary-blue);
            color: white;
        }

        .portfolio-tabs {
            display: flex;
            gap: 2rem;
            margin-bottom: 2rem;
            padding: 0 1.25rem;
            border-bottom: 1px solid #e5e7eb;
        }

        .portfolio-tab {
            background: none;
            border: none;
            padding: 0.75rem 0;
            font-size: 1rem;
            font-weight: 500;
            color: var(--gray-600);
            cursor: pointer;
            position: relative;
            transition: color 0.2s;
        }

        .portfolio-tab.active {
            color: var(--text-dark);
        }

        .portfolio-tab.active::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            right: 0;
            height: 2px;
            background: var(--text-dark);
        }

        .portfolio-tab:hover {
            color: var(--text-dark);
        }

        .portfolio-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            padding: 0 1.25rem;
            margin-bottom: 2rem;
        }

        .portfolio-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s, box-shadow 0.2s;
            cursor: pointer;
        }

        .portfolio-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        }

        .portfolio-card-image {
            width: 100%;
            height: 200px;
            overflow: hidden;
        }

        .portfolio-card-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s;
        }

        .portfolio-card:hover .portfolio-card-image img {
            transform: scale(1.05);
        }

        .portfolio-card-title {
            padding: 1rem;
            margin: 0;
            font-size: 1rem;
            font-weight: 600;
            color: var(--primary-green);
            line-height: 1.4;
        }

        .portfolio-pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 0.5rem;
            padding: 0 1.25rem;
        }

        .pagination-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 1px solid #e5e7eb;
            background: white;
            color: var(--gray-600);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s;
            font-weight: 500;
        }

        .pagination-btn:hover {
            border-color: var(--primary-blue);
            color: var(--primary-blue);
        }

        .pagination-btn.active {
            background: var(--text-dark);
            color: white;
            border-color: var(--text-dark);
        }

        .pagination-dots {
            color: var(--gray-600);
            margin: 0 0.5rem;
        }

        .input-field {
            width: 100%;
            padding: 0.625rem;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            font-size: 0.875rem;
            margin-bottom: 0.75rem;
        }

        .action-buttons {
            display: flex;
            gap: 0.75rem;
            margin-top: 1rem;
        }

        .action-edit-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.375rem;
            padding: 0.5rem 0.75rem;
            background-color: var(--primary-blue);
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .action-edit-btn:hover {
            background-color: #1d4ed8;
        }

        .delete-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.375rem;
            padding: 0.5rem 0.75rem;
            background-color: var(--primary-pink);
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .delete-btn:hover {
            background-color: #b91c77;
        }

        .work-history-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.25rem;
        }

        .work-history-title {
            display: inline-block;
            padding: 0.5rem 0.75rem;
            background-color: var(--primary-pink);
            color: white;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .certification-card {
            background-color: white;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
            margin-bottom: 1.25rem;
            transition: transform 0.3s, box-shadow 0.3s;
        }

        .certification-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .certification-header {
            padding: 1.25rem;
            border-bottom: 1px solid #e5e7eb;
        }

        .certification-title {
            font-size: 1.125rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .certification-title i {
            color: var(--primary-pink);
        }

        .certification-content {
            padding: 1.25rem;
        }

        .certification-detail {
            margin-bottom: 0.75rem;
        }

        .certification-detail strong {
            font-weight: 600;
        }

        .certification-description {
            margin-top: 1rem;
        }

        .certification-description h4 {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .certification-description h4 i {
            color: var(--primary-blue);
        }

        .certification-description p {
            color: var(--gray-600);
        }

        /* Footer */
        footer {
            background: var(--primary-blue);
            padding: 2rem 5%;
            align-items: center;
        }

        .footer-grid {
            display: grid;
            grid-template-columns: repeat(1, 1fr);
            gap: 2rem;
            margin-bottom: 2rem;
        }

        @media (min-width: 768px) {
            .footer-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (min-width: 1024px) {
            .footer-grid {
                grid-template-columns: repeat(4, 1fr);
            }
        }

        .footer-column h3 {
            margin-bottom: 1rem;
            color: var(--text-light);
            font-weight: 600;
        }

        .footer-column a {
            display: block;
            color: var(--text-light);
            text-decoration: none;
            margin-bottom: 0.5rem;
            transition: text-decoration 0.3s ease;
            opacity: 0.8;
        }

        .footer-column a:hover {
            text-decoration: underline;
            opacity: 1;
        }

        .footer-bottom {
            color: var(--text-light);
            text-align: center;
            padding-top: 2rem;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        @media (max-width: 768px) {
            .footer-bottom {
                flex-direction: column;
                text-align: center;
            }
        }

        .footer-bottom p {
            margin: 0;
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 400;
        }

        .social-icons {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .social-icons a {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            margin: 0;
        }

        .social-icons .bi {
            font-size: 1.2rem;
            color: var(--text-light);
            transition: transform 0.3s ease, color 0.3s ease;
        }

        .social-icons a:hover {
            background-color: var(--primary-pink);
            transform: translateY(-3px);
        }

        .footer-bottom a {
            color: var(--text-light);
            margin: 0 10px;
            text-decoration: none;
        }

        .footer-bottom a:hover {
            text-decoration: underline;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }

        .modal.active {
            display: flex;
        }

        .modal-content {
            background-color: white;
            width: 90%;
            max-width: 800px;
            max-height: 90vh;
            border-radius: 10px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            overflow-y: auto;
        }

        .modal-header {
            border-bottom: 1px solid #e5e7eb;
        }

        .modal-header-content {
            display: flex;
            align-items: center;
            height: 80px;
            padding: 0 2rem;
        }

        .modal-body {
            padding: 1.5rem;
        }

        .modal-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #000;
            margin-bottom: 20px;
            text-align: center;
        }

        .modal-textarea {
            width: 100%;
            height: 300px;
            padding: 20px;
            border: 1px solid #ccc;
            border-radius: 20px;
            outline: none;
            resize: none;
            font-size: 16px;
            font-family: inherit;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .modal-textarea:focus {
            border-color: #8b5cf6;
            box-shadow: 0 0 10px rgba(139, 92, 246, 0.5);
        }

        .modal-buttons {
            margin-top: 20px;
            text-align: center;
        }

        .modal-back-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            background: transparent;
            color: #000;
            text-decoration: underline;
        }

        .modal-back-btn:hover {
            color: #8b5cf6;
        }

        .modal-next-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            background-color: var(--primary-pink);
            color: #fff;
            margin-left: 10px;
        }

        .modal-next-btn:hover {
            background-color: #b91c77;
        }

        /* Portfolio Modal Theme Colors */
        #portfolioAddModal .modal-next-btn {
            background-color: #CD208B;
        }

        #portfolioAddModal .modal-next-btn:hover {
            background-color: #b91c77;
        }

        #portfolioAddModal h2 {
            color: #004AAD;
        }

        /* Portfolio Modal Content Buttons */
        #portfolioAddModal button[style*="border: 2px solid"] {
            border-color: #004AAD !important;
            color: #004AAD !important;
        }

        #portfolioAddModal button[style*="border: 2px solid"]:hover {
            background-color: #004AAD !important;
            color: white !important;
        }

        /* Edit Profile Modal */
        .profile-form {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        @media (min-width: 1024px) {
            .profile-form {
                flex-direction: row;
                align-items: flex-start;
            }
        }

        .profile-photo-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.75rem;
        }

        .profile-photo {
            width: 150px;
            height: 150px;
            border: 1px solid #e5e7eb;
            overflow: hidden;
            border-radius: 8px;
        }

        .profile-photo img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .upload-btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.375rem;
            padding: 0.5rem 0.75rem;
            background-color: #f3f4f6;
            color: #333;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .upload-btn:hover {
            background-color: #e5e7eb;
        }

        .upload-note {
            font-size: 0.75rem;
            color: #6b7280;
        }

        .profile-details-section {
            flex: 1;
        }

        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group label {
            display: block;
            font-size: 0.875rem;
            font-weight: 500;
            margin-bottom: 0.375rem;
        }

        .form-control {
            width: 100%;
            padding: 0.625rem;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            font-size: 0.875rem;
            background-color: #f9fafb;
            color: #333;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1);
        }

        .form-control[readonly] {
            background-color: #f3f4f6;
            cursor: not-allowed;
        }

        .form-row {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        @media (min-width: 640px) {
            .form-row {
                flex-direction: row;
            }
        }

        .form-col {
            flex: 1;
        }

        .next-btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            background-color: var(--primary-pink);
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
            width: 100%;
            margin-top: 1rem;
        }

        .next-btn:hover {
            background-color: #b91c77;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .header-content {
                padding: 0 1rem;
            }

            .main-content {
                padding: 1rem;
            }

            .profile-name {
                font-size: 1.5rem;
            }

            .profile-title {
                font-size: 1rem;
            }

            .modal-content {
                width: 95%;
                margin: 1rem;
            }

            .modal-body {
                padding: 1rem;
            }

            .profile-form {
                flex-direction: column;
            }
        }

        /* Utility Classes */
        .hidden {
            display: none;
        }

        .flex {
            display: flex;
        }

        .items-center {
            align-items: center;
        }

        .justify-between {
            justify-content: space-between;
        }

        .gap-2 {
            gap: 0.5rem;
        }

        .gap-4 {
            gap: 1rem;
        }

        .mb-4 {
            margin-bottom: 1rem;
        }

        .mb-6 {
            margin-bottom: 1.5rem;
        }

        .text-center {
            text-align: center;
        }

        .font-bold {
            font-weight: bold;
        }

        .font-semibold {
            font-weight: 600;
        }

        .text-sm {
            font-size: 0.875rem;
        }

        .text-lg {
            font-size: 1.125rem;
        }

        .text-xl {
            font-size: 1.25rem;
        }

        .text-2xl {
            font-size: 1.5rem;
        }

        .text-3xl {
            font-size: 1.875rem;
        }

        .w-full {
            width: 100%;
        }

        .h-full {
            height: 100%;
        }

        .rounded {
            border-radius: 0.25rem;
        }

        .rounded-lg {
            border-radius: 0.5rem;
        }

        .shadow-sm {
            box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        }

        .shadow-md {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        .transition-all {
            transition: all 0.3s ease;
        }

        .hover\:shadow-md:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        .hover\:transform:hover {
            transform: translateY(-2px);
        }

        /* Star rating */
        .star-rating {
            color: #FBBF24;
        }

        /* Toggle switch */
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 44px;
            height: 24px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #22C55E;
            transition: .4s;
            border-radius: 34px;
        }

        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
            transform: translateX(20px);
        }

        /* Work History Styles */
        .work-history-summary {
            background-color: #f9fafb;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1.5rem;
        }

        .work-history-summary h3 {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .work-history-summary p {
            color: #374151;
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
            line-height: 1.5;
        }

        .work-history-summary .show-more-btn {
            color: var(--primary-blue);
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            background: none;
            border: none;
            padding: 0;
        }

        .work-history-summary .ai-note {
            font-size: 0.75rem;
            color: #6b7280;
            margin-top: 0.75rem;
        }

        .skills-section {
            margin-bottom: 1.5rem;
        }

        .skills-section h3 {
            font-size: 0.875rem;
            font-weight: 500;
            color: #374151;
            margin-bottom: 0.75rem;
        }

        .skills-list {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .skill-tag {
            background-color: #f3f4f6;
            color: #374151;
            font-size: 0.75rem;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
        }

        .job-tabs {
            border-bottom: 1px solid #e5e7eb;
            margin-bottom: 1.5rem;
        }

        .job-tabs .tab-list {
            display: flex;
            gap: 1.5rem;
        }

        .job-tab {
            background: none;
            border: none;
            padding: 0.5rem 0;
            font-size: 1rem;
            font-weight: 500;
            color: #6b7280;
            cursor: pointer;
            position: relative;
            transition: color 0.2s;
        }

        .job-tab.active {
            color: #000;
        }

        .job-tab.active::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            right: 0;
            height: 2px;
            background: #000;
        }

        .job-tab:hover {
            color: #000;
        }

        .job-list {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }

        .job-item {
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 1.5rem;
        }

        .job-item:last-child {
            border-bottom: none;
            padding-bottom: 0;
        }

        .job-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 0.25rem;
        }

        .job-title {
            color: var(--primary-blue);
            font-weight: 500;
            margin: 0;
        }

        .job-menu-btn {
            background: none;
            border: none;
            color: #6b7280;
            border-radius: 50%;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .job-menu-btn:hover {
            background-color: #f3f4f6;
        }

        .job-rating {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.25rem;
        }

        .job-rating .stars {
            display: flex;
            color: #FBBF24;
        }

        .job-rating .rating-score {
            font-weight: 500;
        }

        .job-rating .separator {
            color: #6b7280;
        }

        .job-date {
            font-size: 0.875rem;
            color: #6b7280;
            margin-bottom: 0.75rem;
        }

        .job-feedback {
            font-size: 0.875rem;
            color: #374151;
            margin-bottom: 0.75rem;
        }

        .job-earnings {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .job-total {
            font-weight: 500;
        }

        .job-rate, .job-hours {
            font-size: 0.875rem;
            color: #6b7280;
        }

        /* Side Notification Styles */
        .side-notification {
            position: fixed;
            bottom: 20px;
            right: -400px;
            width: 350px;
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            z-index: 10000;
            transition: right 0.4s ease-in-out;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .side-notification.show {
            right: 20px;
        }

        .notification-content {
            display: flex;
            align-items: center;
            padding: 16px 20px;
            gap: 12px;
        }

        .notification-icon {
            flex-shrink: 0;
            width: 40px;
            height: 40px;
            background: #10b981;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }

        .notification-text {
            flex: 1;
            color: #374151;
            font-size: 14px;
            font-weight: 500;
            line-height: 1.4;
        }

        .notification-close {
            flex-shrink: 0;
            background: none;
            border: none;
            color: #9ca3af;
            font-size: 16px;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: color 0.2s ease;
        }

        .notification-close:hover {
            color: #6b7280;
            background: #f3f4f6;
        }

        /* Animation for the notification */
        @keyframes slideInRight {
            from {
                right: -400px;
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                right: 20px;
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideOutRight {
            from {
                right: 20px;
                opacity: 1;
                transform: translateY(0);
            }
            to {
                right: -400px;
                opacity: 0;
                transform: translateY(10px);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header Section -->
        <header class="header">
            <div class="header-content">
                <a href="{{ url_for('landing_page') }}" class="logo">
                    <div class="logo-image">
                        <img src="{{ url_for('static', filename='img/logo.png') }}" alt="GigGenius Logo">
                    </div>
                    <span class="logo-text">GigGenius</span>
                </a>

                <nav class="nav-links">
                    <a href="{{ url_for('genius_page') }}">Find Gigs</a>
                    <a href="{{ url_for('my_proposal') }}">Proposals</a>
                    <div class="dropdown">
                        <a href="#" class="dropdown-trigger">Contracts <i class="fas fa-chevron-down"></i></a>
                        <div class="dropdown-menu">
                            <a href="{{ url_for('tracker') }}">Log Works</a>
                        </div>
                    </div>
                    <div class="dropdown">
                        <a href="#" class="dropdown-trigger">Earnings <i class="fas fa-chevron-down"></i></a>
                        <div class="dropdown-menu">
                            <a href="{{ url_for('billing_and_earnings') }}">Billings and Earnings</a>
                            <a href="{{ url_for('withdraw_earnings') }}">Withdraw Earnings</a>
                            <a href="{{ url_for('tax_info') }}">Tax Info</a>
                        </div>
                    </div>
                    <a href="#">Messages</a>
                </nav>

                <div class="header-actions">
                    <button class="gigs-btn">Gigs</button>

                    <div class="search-wrapper">
                        <input type="text" placeholder="Search..." class="header-search">
                        <i class="fas fa-search"></i>
                    </div>
                    <div class="notification-dropdown">
                        <button class="notification-btn" id="notificationBtn" aria-label="Notifications">
                            <i class="fas fa-bell"></i>
                            <span class="notification-badge">3</span>
                        </button>
                        <div class="notification-dropdown-menu" id="notificationDropdown">
                            <div class="notification-header">
                                <h3>Notifications</h3>
                                <button class="mark-all-read" id="markAllRead">Mark all as read</button>
                            </div>
                            <ul class="notification-list">
                                <li class="notification-item unread">
                                    <div class="notification-icon">
                                        <i class="fas fa-briefcase"></i>
                                    </div>
                                    <div class="notification-content">
                                        <p class="notification-text">You have a new job offer from <strong>Tech Innovations Inc</strong></p>
                                        <span class="notification-time">2 hours ago</span>
                                    </div>
                                </li>
                                <li class="notification-item unread">
                                    <div class="notification-icon">
                                        <i class="fas fa-comment"></i>
                                    </div>
                                    <div class="notification-content">
                                        <p class="notification-text"><strong>John Smith</strong> sent you a message</p>
                                        <span class="notification-time">5 hours ago</span>
                                    </div>
                                </li>
                                <li class="notification-item">
                                    <div class="notification-icon">
                                        <i class="fas fa-dollar-sign"></i>
                                    </div>
                                    <div class="notification-content">
                                        <p class="notification-text">Payment of <strong>$250</strong> received from <strong>Global Marketing Solutions</strong></p>
                                        <span class="notification-time">Yesterday</span>
                                    </div>
                                </li>
                                <li class="notification-item">
                                    <div class="notification-icon">
                                        <i class="fas fa-star"></i>
                                    </div>
                                    <div class="notification-content">
                                        <p class="notification-text"><strong>Digital Transformation Co</strong> left you a 5-star review</p>
                                        <span class="notification-time">2 days ago</span>
                                    </div>
                                </li>
                                <li class="notification-item">
                                    <div class="notification-icon">
                                        <i class="fas fa-check-circle"></i>
                                    </div>
                                    <div class="notification-content">
                                        <p class="notification-text">Your profile has been verified</p>
                                        <span class="notification-time">3 days ago</span>
                                    </div>
                                </li>
                            </ul>
                            <div class="notification-footer">
                                <a href="#">View all notifications</a>
                            </div>
                        </div>
                    </div>
                    <div class="profile-dropdown">
                        <div class="profile-icon" id="profileIcon">
                            <img src="{{ genius.profile_picture_url }}" alt="Profile">
                        </div>
                        <div class="profile-dropdown-menu" id="profileDropdown">
                            <a href="{{ url_for('genius_profile') }}">Your Profile</a>
                            <a href="{{ url_for('withdraw_earnings', section='profile-settings') }}">Settings</a>
                            <a href="#">Logout</a>
                        </div>
                    </div>
                    <button class="mobile-menu-btn" id="mobileMenuBtn" aria-label="Menu">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>

            <div class="mobile-menu" id="mobileMenu">
                <a href="{{ url_for('genius_page') }}">Find Gigs</a>
                <a href="{{ url_for('my_proposal') }}">Proposals</a>
                <a href="#">Contracts</a>
                <a href="{{ url_for('tracker') }}">Log Works</a>
                <a href="#">Earnings</a>
                <a href="{{ url_for('billing_and_earnings') }}">Billings and Earnings</a>
                <a href="{{ url_for('withdraw_earnings') }}">Withdraw Earnings</a>
                <a href="{{ url_for('tax_info') }}">Tax Info</a>
                <a href="#">Messages</a>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Profile Section -->
            <section class="profile-section">
                <!-- Name and Title above video -->
                <div class="profile-header">
                    <h1 class="profile-name" id="profileName">{{ genius.first_name }} {{ genius.last_name }}</h1>
                    <p class="profile-title">
                        <i class="fas fa-briefcase"></i>
                        {% if genius.position %}
                            <span id="profilePosition">{{ genius.position }}</span>
                        {% endif %}
                    </p>
                </div>
                
                <div class="profile-row">
                    <!-- Video Card -->
                    <div class="video-card">
                        <div class="video-container" id="videoContainer">
                            <!-- Video Upload Placeholder (shown when no video) -->
                            <div class="video-placeholder" id="videoPlaceholder" style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%; background: #f8f9fa; border: 2px dashed #dee2e6; border-radius: 8px;">
                                <div style="text-align: center; color: #6c757d;">
                                    <i class="fas fa-video" style="font-size: 3rem; margin-bottom: 1rem; color: #adb5bd;"></i>
                                    <h3 style="margin: 0 0 0.5rem 0; font-size: 1.25rem; font-weight: 600;">Add a video introduction</h3>
                                    <p style="margin: 0 0 1.5rem 0; font-size: 0.875rem;">Show clients who you are with a video introduction</p>
                                    <button class="upload-video-btn" id="uploadVideoBtn" style="background: var(--primary-blue); color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 6px; cursor: pointer; font-weight: 600;">
                                        <i class="fas fa-upload"></i> Upload Video
                                    </button>
                                    <input type="file" id="videoUpload" accept="video/*" style="display: none;">
                                </div>
                            </div>

                            <!-- Video Player (shown when video exists) -->
                            <div class="video-player" id="videoPlayer" style="display: none; position: relative; width: 100%; height: 100%;">
                                <video id="profileVideo" controls poster="https://images.unsplash.com/photo-1498050108023-c5249f4df085?w=800&h=450&fit=crop" style="width: 100%; height: 100%; object-fit: cover;">
                                    <source src="#" type="video/mp4">
                                    Your browser does not support the video tag.
                                </video>

                                <!-- Video Controls -->
                                <div class="video-controls" style="position: absolute; top: 10px; right: 10px; display: flex; gap: 0.5rem;">
                                    <button class="video-control-btn" id="deleteVideoBtn" style="background: rgba(220, 53, 69, 0.9); color: white; border: none; padding: 0.5rem; border-radius: 4px; cursor: pointer;" title="Delete Video">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    <button class="video-control-btn" id="replaceVideoBtn" style="background: rgba(40, 167, 69, 0.9); color: white; border: none; padding: 0.5rem; border-radius: 4px; cursor: pointer;" title="Replace Video">
                                        <i class="fas fa-upload"></i>
                                    </button>
                                </div>

                                <!-- Fullscreen Button -->
                                <button class="expand-btn" id="expandBtn" aria-label="Expand video" style="position: absolute; bottom: 10px; right: 10px; background: rgba(0, 0, 0, 0.7); color: white; border: none; padding: 0.5rem 1rem; border-radius: 4px; cursor: pointer;">
                                    <i class="fas fa-expand"></i> Fullscreen
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Profile Card -->
                    <div class="profile-card">
                        <div class="profile-content">
                            <!-- Stats - Updated to include 4 items -->
                            <div class="profile-stats">
                                <div class="stat-item">
                                    <div class="stat-value" id="hourlyRate">
                                        {% if genius.hourly_rate %}
                                            ${{ genius.hourly_rate }}
                                        {% else %}
                                            <span style="color:#bbb;">No rate set</span>
                                        {% endif %}
                                    </div>
                                    <div class="stat-label">Hourly Rate</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value" id="totalEarnings">$0</div>
                                    <div class="stat-label">Total Earnings</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value" id="hired">0</div>
                                    <div class="stat-label">Hired</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value" id="completedJobs">0</div>
                                    <div class="stat-label">Completed</div>
                                </div>
                            </div>

                            <!-- Professional Summary -->
                            <div class="profile-summary">
                                <div class="flex items-center justify-between mb-4">
                                    <h3><i class="fas fa-user-circle"></i> Professional Summary</h3>
                                    <button class="edit-btn" id="editSummaryBtn">
                                        <i class="fas fa-edit"></i>
                                        <span>Edit</span>
                                    </button>
                                </div>
                                <p class="summary-text" id="summaryText">
                                    {% if genius.professional_sum %}
                                        {{ genius.professional_sum }}
                                    {% else %}
                                        <span style="color: #6b7280; font-style: italic;">
                                            Add a professional summary to showcase your skills and experience to potential clients.
                                            Click the Edit button to get started.
                                        </span>
                                    {% endif %}
                                </p>
                                <span class="show-more hidden" id="showMore"><i class="fas fa-plus-circle"></i> Show More</span>
                            </div>

                            <!-- Profile Fields -->
                            <div class="profile-fields">
                                <div class="field-group">
                                    <label>Availability</label>
                                    <input type="text" value="{% if genius.availability == 'fulltime' %}Full-Time{% elif genius.availability == 'parttime' %}Part-Time{% else %}{{ genius.availability if genius.availability else '' }}{% endif %}" readonly class="field-input" id="displayAvailability">
                                </div>
                                <div class="field-group">
                                    <label>Language</label>
                                    <input type="text" value="{{ genius.language if genius.language else 'English' }}" readonly class="field-input" id="displayLanguage">
                                </div>
                                <div class="field-group">
                                    <label>Country</label>
                                    <input type="text" value="{{ genius.country if genius.country else '' }}" readonly class="field-input" id="displayCountryField">
                                </div>
                            </div>

                            <!-- Edit Profile Button -->
                            <button class="edit-profile-btn" id="editProfileBtn">
                                <i class="fas fa-user-edit"></i>
                                <span>Edit Profile</span>
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Portfolio and Work History Section -->
            <section class="portfolio-section">
                <div class="section-row">
                    <!-- Introduction and Portfolio -->
                    <div class="section-card">
                        <div class="section-content">
                            <div class="introduction-header">
                                <h2 class="section-title" id="introduction"><i class="fas fa-info-circle"></i> Introduction</h2>
                                <button class="edit-btn" id="editIntroductionBtn">
                                    <i class="fas fa-edit"></i>
                                    <span>Edit</span>
                                </button>
                            </div>
                            <textarea class="textarea" id="introductionDisplay" placeholder="Write your introduction not less than 300 words..." readonly>{{ genius.introduction if genius.introduction else '' }}</textarea>
                        </div>

                        <!-- Portfolio Header -->
                        <div class="portfolio-header">
                            <h2 class="portfolio-title">Portfolio</h2>
                            <div class="portfolio-actions">
                                <button class="portfolio-add-btn" id="portfolioAddBtn">
                                    <i class="fas fa-plus"></i>
                                </button>
                                <button class="portfolio-refresh-btn">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Portfolio Tabs -->
                        <div class="portfolio-tabs">
                            <button class="portfolio-tab active" data-tab="published">Published</button>
                            <button class="portfolio-tab" data-tab="drafts">Drafts</button>
                        </div>

                        <!-- Portfolio Grid -->
                        <div class="portfolio-grid" id="portfolioGrid">
                            <!-- Published Projects (shown by default) -->
                            <div class="portfolio-content" id="publishedContent">
                                {% if genius.portfolio and genius.portfolio.published and genius.portfolio.published|length > 0 %}
                                    {% for project in genius.portfolio.published %}
                                        <div class="portfolio-card" data-project-id="{{ project.id }}" data-project-title="{{ project.project_title }}" data-project-role="{{ project.project_role or '' }}" data-project-description="{{ project.project_description }}" data-project-skills="{{ project.skills_and_deliverables or '' }}">
                                            <div class="portfolio-card-image">
                                                {% if project.project_image_filename %}
                                                    <img src="/api/portfolio-image/{{ project.id }}" alt="{{ project.project_title }}" style="width: 100%; height: 200px; object-fit: cover; border-radius: 8px;">
                                                {% else %}
                                                    <div style="width: 100%; height: 200px; background: #f0f0f0; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: #666;">
                                                        <i class="fas fa-image" style="font-size: 2rem;"></i>
                                                    </div>
                                                {% endif %}
                                            </div>
                                            <div class="portfolio-card-content" style="padding: 1rem;">
                                                <h3 style="margin: 0 0 0.5rem 0; font-size: 1.1rem; font-weight: 600;">{{ project.project_title }}</h3>
                                                {% if project.project_role %}
                                                    <p style="margin: 0 0 0.5rem 0; color: #666; font-size: 0.9rem;">{{ project.project_role }}</p>
                                                {% endif %}
                                                <p style="margin: 0; color: #888; font-size: 0.8rem; line-height: 1.4;">{{ project.project_description[:100] }}{% if project.project_description|length > 100 %}...{% endif %}</p>
                                                {% if project.skills_and_deliverables %}
                                                    <div style="margin-top: 0.5rem;">
                                                        <span style="background: #e3f2fd; color: #1976d2; padding: 0.2rem 0.5rem; border-radius: 12px; font-size: 0.7rem;">{{ project.skills_and_deliverables }}</span>
                                                    </div>
                                                {% endif %}
                                            </div>
                                        </div>
                                    {% endfor %}
                                {% else %}
                                    <div style="padding:2rem;text-align:center;color:#888;">No published projects yet. Click + to add your first project.</div>
                                {% endif %}
                            </div>

                            <!-- Draft Projects (hidden by default) -->
                            <div class="portfolio-content" id="draftsContent" style="display: none;">
                                {% if genius.portfolio and genius.portfolio.drafts and genius.portfolio.drafts|length > 0 %}
                                    {% for project in genius.portfolio.drafts %}
                                        <div class="portfolio-card" data-project-id="{{ project.id }}" data-project-title="{{ project.project_title }}" data-project-role="{{ project.project_role or '' }}" data-project-description="{{ project.project_description }}" data-project-skills="{{ project.skills_and_deliverables or '' }}">
                                            <div class="portfolio-card-image">
                                                {% if project.project_image_filename %}
                                                    <img src="/api/portfolio-image/{{ project.id }}" alt="{{ project.project_title }}" style="width: 100%; height: 200px; object-fit: cover; border-radius: 8px;">
                                                {% else %}
                                                    <div style="width: 100%; height: 200px; background: #f0f0f0; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: #666;">
                                                        <i class="fas fa-image" style="font-size: 2rem;"></i>
                                                    </div>
                                                {% endif %}
                                            </div>
                                            <div class="portfolio-card-content" style="padding: 1rem;">
                                                <h3 style="margin: 0 0 0.5rem 0; font-size: 1.1rem; font-weight: 600;">{{ project.project_title }}</h3>
                                                {% if project.project_role %}
                                                    <p style="margin: 0 0 0.5rem 0; color: #666; font-size: 0.9rem;">{{ project.project_role }}</p>
                                                {% endif %}
                                                <p style="margin: 0; color: #888; font-size: 0.8rem; line-height: 1.4;">{{ project.project_description[:100] }}{% if project.project_description|length > 100 %}...{% endif %}</p>
                                                {% if project.skills_and_deliverables %}
                                                    <div style="margin-top: 0.5rem;">
                                                        <span style="background: #fff3e0; color: #f57c00; padding: 0.2rem 0.5rem; border-radius: 12px; font-size: 0.7rem;">{{ project.skills_and_deliverables }}</span>
                                                    </div>
                                                {% endif %}
                                                <div style="margin-top: 0.5rem;">
                                                    <span style="background: #ffecb3; color: #f57c00; padding: 0.2rem 0.5rem; border-radius: 12px; font-size: 0.7rem;">Draft</span>
                                                </div>
                                            </div>
                                        </div>
                                    {% endfor %}
                                {% else %}
                                    <div style="padding:2rem;text-align:center;color:#888;">No draft projects yet.</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Portfolio Pagination (keep or remove as needed) -->
                        <div class="portfolio-pagination">
                            <button class="pagination-btn prev">
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            <button class="pagination-btn active">1</button>
                            <button class="pagination-btn">2</button>
                            <button class="pagination-btn">3</button>
                            <button class="pagination-btn">4</button>
                            <span class="pagination-dots">...</span>
                            <button class="pagination-btn next">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Work History -->
                    <div class="section-card">
                        <div class="section-content">
                            <h2 class="text-lg font-semibold mb-6">Work History</h2>

                            <!-- Work History Summary -->
                            <div class="work-history-summary">
                                {% if genius.work_history and genius.work_history|length > 0 %}
                                    {# Render work history summary dynamically #}
                                {% else %}
                                    <p style="color:#888;">No work history yet.</p>
                                {% endif %}
                            </div>

                            <!-- Skills Used -->
                            <div class="skills-section">
                                <h3>Skills used in past work</h3>
                                <div class="skills-list">
                                    <span class="skill-tag">JavaScript</span>
                                    <span class="skill-tag">React</span>
                                    <span class="skill-tag">Node.js</span>
                                    <span class="skill-tag">Python</span>
                                    <span class="skill-tag">Digital Marketing</span>
                                    <span class="skill-tag">SEO</span>
                                    <span class="skill-tag">UI/UX Design</span>
                                    <span class="skill-tag">Database Design</span>
                                </div>
                            </div>

                            <!-- Job Tabs -->
                            <div class="job-tabs">
                                <div class="tab-list">
                                    <button class="job-tab active" data-tab="completed">Completed jobs (12)</button>
                                    <button class="job-tab" data-tab="in-progress">In progress (3)</button>
                                </div>
                            </div>

                            <!-- Job List -->
                            <div class="job-list">
                                {% if genius.jobs and genius.jobs|length > 0 %}
                                    {% for job in genius.jobs %}
                                        <div class="job-item">
                                            <!-- Render job info here -->
                                        </div>
                                    {% endfor %}
                                {% else %}
                                    <div style="padding:2rem;text-align:center;color:#888;">No jobs to display yet.</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <!-- Footer -->
        <footer>
            <div class="footer-grid">
                <div class="footer-column">
                    <h3>For Clients</h3>
                    <a href="#">How to Hire</a>
                    <a href="#">Marketplace</a>
                    <a href="#">Payroll Services</a>
                    <a href="#">Service Catalog</a>
                    <a href="#">Business Networking</a>
                    <a href="#">PH Business Loan</a>
                </div>
                <div class="footer-column">
                    <h3>For Geniuses</h3>
                    <a href="#">How It Works?</a>
                    <a href="#">Why Can't I Apply?</a>
                    <a href="#">Direct Contracts</a>
                    <a href="#">Find Mentors</a>
                    <a href="#">Mentor Application</a>
                    <a href="#">PH Health Insurance</a>
                    <a href="#">PH Life Insurance</a>
                </div>
                <div class="footer-column">
                    <h3>Resources</h3>
                    <a href="#">Help & Support</a>
                    <a href="#">News & Events</a>
                    <a href="#">Affiliate Program</a>
                </div>
                <div class="footer-column">
                    <h3>Company</h3>
                    <a href="#">About Us</a>
                    <a href="#">Contact Us</a>
                    <a href="#">Charity Projects</a>
                </div>
            </div>
            <div class="footer-bottom">
                <p>
                    Follow Us:
                    <span class="social-icons">
                        <a href="https://www.facebook.com/giggenius.io"><i class="bi bi-facebook"></i></a>
                        <a href="https://www.instagram.com/giggenius.io/"><i class="bi bi-instagram"></i></a>
                        <a href="https://twitter.com/giggenius_io"><i class="bi bi-twitter-x"></i></a>
                        <a href="#"><i class="bi bi-tiktok"></i></a>
                        <a href="https://www.youtube.com/@giggenius"><i class="bi bi-youtube"></i></a>
                        <a href="https://www.linkedin.com/company/gig-genius/"><i class="bi bi-linkedin"></i></a>
                    </span>
                </p>
                <p>©2025 GigGenius by <a href="https://genuinelysolutions.com/">Genuinely Business Solutions</a></p>
                <p>
                    <a href="#">Terms of Service</a> |
                    <a href="#">Privacy Policy</a>
                </p>
            </div>
        </footer>
    </div>

    <!-- Side Notification -->
    <div class="side-notification" id="sideNotification">
        <div class="notification-content">
            <div class="notification-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="notification-text">
                <span id="notificationMessage">Professional summary has been updated</span>
            </div>
            <button class="notification-close" id="notificationClose">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>

    <!-- Modal for Professional Summary -->
    <div class="modal" id="professionalSummaryModal">
        <div class="modal-content">
            <!-- Header Section -->
            <header class="modal-header">
                <div class="modal-header-content">
                    <div class="logo">
                        <div class="logo-image">
                            <img src="{{ url_for('static', filename='img/logo.png') }}" alt="GigGenius Logo">
                        </div>
                        <span class="logo-text">GigGenius</span>
                    </div>
                </div>
            </header>

            <!-- Form Section -->
            <div class="modal-body">
                <h2 class="modal-title">Edit Professional Summary *</h2>
                <textarea class="modal-textarea" id="professionalSummaryTextarea" placeholder="Write something..."></textarea>
                <div class="modal-buttons">
                    <button class="modal-back-btn" id="summaryBackBtn">Back</button>
                    <button class="modal-next-btn" id="summaryNextBtn">Next</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for Introduction -->
    <div class="modal" id="introductionModal">
        <div class="modal-content">
            <!-- Header Section -->
            <header class="modal-header">
                <div class="modal-header-content">
                    <div class="logo">
                        <div class="logo-image">
                            <img src="{{ url_for('static', filename='img/logo.png') }}" alt="GigGenius Logo">
                        </div>
                        <span class="logo-text">GigGenius</span>
                    </div>
                </div>
            </header>

            <!-- Form Section -->
            <div class="modal-body">
                <h2 class="modal-title">Edit Introduction *</h2>
                <textarea class="modal-textarea" id="introductionTextarea" placeholder="Write something...">{{ genius.introduction or '' }}</textarea>
                <div class="modal-buttons">
                    <button class="modal-back-btn" id="introBackBtn">Back</button>
                    <button class="modal-next-btn" id="introNextBtn">Next</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for Edit Profile -->
    <div class="modal" id="editProfileModal">
        <div class="modal-content">
            <!-- Header Section -->
            <header class="modal-header">
                <div class="modal-header-content">
                    <div class="logo">
                        <div class="logo-image">
                            <img src="{{ url_for('static', filename='img/logo.png') }}" alt="GigGenius Logo">
                        </div>
                        <span class="logo-text">GigGenius</span>
                    </div>
                </div>
            </header>

            <!-- Main Content -->
            <div class="modal-body">
                <div class="welcome-section" style="margin-bottom: 2rem;">
                    <h1 class="section-title" style="margin-bottom: 1rem;">Welcome <span style="color: var(--primary-pink);">{{ genius.first_name }} {{ genius.last_name }}</span></h1>
                    <div class="profile-info-display" style="display: flex; align-items: center; gap: 1rem; margin-bottom: 1.5rem;">
                        <div class="info-item" style="display: flex; align-items: center; gap: 0.5rem;">
                            <i class="fas fa-briefcase" style="color: var(--primary-blue);"></i>
                            <span id="displayPosition" style="font-weight: 500;">{{ genius.position }}</span>
                        </div>
                        <div class="info-item" style="display: flex; align-items: center; gap: 0.5rem;">
                            <i class="fas fa-map-marker-alt" style="color: var(--primary-blue);"></i>
                            <span id="displayCountry" style="font-weight: 500;">{{ genius.country }}</span>
                        </div>
                    </div>
                </div>

                <div class="profile-form">
                    <!-- Profile Photo Section -->
                    <div class="profile-photo-section">
                        <div class="profile-photo">
                            <img src="{{ genius.profile_picture_url }}" alt="Profile Photo">
                        </div>
                        <label for="profile-upload" class="upload-btn">
                            <i class="fas fa-plus"></i>
                            <span>Upload</span>
                        </label>
                        <input type="file" id="profile-upload" accept="image/*" style="display: none;">
                        <p class="upload-note">Maximum of 2MB</p>
                    </div>

                    <!-- Profile Details Section -->
                    <div class="profile-details-section">
                        <h2 class="section-title">Edit your profile</h2>

                        <div class="form-group">
                            <label for="email">Email</label>
                            <input type="email" id="email" class="form-control" value="{{ genius.email or '' }}" readonly>
                        </div>

                        <div class="form-group">
                            <label for="mobile">Mobile No.</label>
                            <input type="tel" id="mobile" class="form-control" value="{{ genius.mobile or '' }}">
                        </div>

                        <div class="form-group">
                            <label for="position">Position</label>
                            <input type="text" id="position" class="form-control" value="{{ genius.position or '' }}">
                        </div>

                        <div class="form-row">
                            <div class="form-col">
                                <div class="form-group">
                                    <label for="expertise">Expertise Level</label>
                                    <select id="expertise" class="form-control">
                                        <option value="Expert" {% if genius.expertise == 'Expert' %}selected{% endif %}>Expert</option>
                                        <option value="Intermediate" {% if genius.expertise == 'Intermediate' %}selected{% endif %}>Intermediate</option>
                                        <option value="Beginner" {% if genius.expertise == 'Beginner' %}selected{% endif %}>Beginner</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-col">
                                <div class="form-group">
                                    <label for="rate">Rate per Hour (USD)</label>
                                    <input type="number" id="rate" class="form-control" value="{{ genius.hourly_rate or '' }}">
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="availability">Availability</label>
                            <select id="availability" class="form-control">
                                <option value="fulltime" {% if genius.availability == 'fulltime' %}selected{% endif %}>Full-Time</option>
                                <option value="parttime" {% if genius.availability == 'parttime' %}selected{% endif %}>Part-Time</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="country">Country</label>
                            <select id="country" class="form-control">
                                <option value="">Select Country</option>
                                <option value="Philippines" {% if genius.country == 'Philippines' %}selected{% endif %}>Philippines</option>
                                <option value="Nigeria" {% if genius.country == 'Nigeria' %}selected{% endif %}>Nigeria</option>
                                <option value="United States" {% if genius.country == 'United States' %}selected{% endif %}>United States</option>
                                <option value="United Kingdom" {% if genius.country == 'United Kingdom' %}selected{% endif %}>United Kingdom</option>
                                <option value="Canada" {% if genius.country == 'Canada' %}selected{% endif %}>Canada</option>
                                <option value="Australia" {% if genius.country == 'Australia' %}selected{% endif %}>Australia</option>
                                <option value="Germany" {% if genius.country == 'Germany' %}selected{% endif %}>Germany</option>
                                <option value="France" {% if genius.country == 'France' %}selected{% endif %}>France</option>
                                <option value="Japan" {% if genius.country == 'Japan' %}selected{% endif %}>Japan</option>
                                <option value="China" {% if genius.country == 'China' %}selected{% endif %}>China</option>
                                <option value="India" {% if genius.country == 'India' %}selected{% endif %}>India</option>
                                <option value="Brazil" {% if genius.country == 'Brazil' %}selected{% endif %}>Brazil</option>
                                <option value="South Africa" {% if genius.country == 'South Africa' %}selected{% endif %}>South Africa</option>
                                <!-- Add a fallback option for any other country value -->
                                {% if genius.country and genius.country not in ['Philippines', 'Nigeria', 'United States', 'United Kingdom', 'Canada', 'Australia', 'Germany', 'France', 'Japan', 'China', 'India', 'Brazil', 'South Africa'] %}
                                <option value="{{ genius.country }}" selected>{{ genius.country }}</option>
                                {% endif %}
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="language">Language</label>
                            <select id="language" class="form-control">
                                <option value="English" selected>English</option>
                                <option value="French">French</option>
                                <option value="Spanish">Spanish</option>
                                <option value="German">German</option>
                                <option value="Chinese">Chinese</option>
                                <option value="Japanese">Japanese</option>
                                <option value="Arabic">Arabic</option>
                                <option value="Russian">Russian</option>
                                <option value="Portuguese">Portuguese</option>
                                <option value="Hindi">Hindi</option>
                            </select>
                        </div>

                        <div class="modal-buttons" style="text-align: right;">
                            <button class="modal-back-btn" id="profileBackBtn">Back</button>
                            <button class="modal-next-btn" id="saveProfileBtn">Next</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for Portfolio View/Publish -->
    <div class="modal" id="portfolioViewModal">
        <div class="modal-content" style="max-width: 900px;">
            <!-- Header Section -->
            <header class="modal-header">
                <div class="modal-header-content">
                    <div class="logo">
                        <div class="logo-image">
                            <img src="{{ url_for('static', filename='img/logo.png') }}" alt="GigGenius Logo">
                        </div>
                        <span class="logo-text">GigGenius</span>
                    </div>
                    <button class="modal-close-btn" id="portfolioViewCloseBtn" style="background: none; border: none; font-size: 1.5rem; cursor: pointer; color: #666;">×</button>
                </div>
            </header>

            <!-- Portfolio Content -->
            <div class="modal-body" style="padding: 2rem;">
                <div class="portfolio-view-container">
                    <!-- Project Header -->
                    <div class="portfolio-project-header" style="margin-bottom: 2rem;">
                        <h1 class="project-title" style="font-size: 2rem; font-weight: 700; color: #333; margin-bottom: 0.5rem;">Sample Project Title</h1>
                        <div class="project-meta" style="display: flex; align-items: center; gap: 1rem; margin-bottom: 1rem;">
                            <span class="project-role" style="color: #666; font-size: 1rem;">My role: <strong>Full Stack Developer</strong></span>
                            <span class="project-date" style="color: #666; font-size: 0.9rem;">Published on Feb 24, 2025</span>
                        </div>
                        <button class="copy-link-btn" style="background: #f0f0f0; border: 1px solid #ddd; padding: 0.5rem 1rem; border-radius: 6px; cursor: pointer; font-size: 0.9rem;">
                            <i class="fas fa-link"></i> Copy link
                        </button>
                    </div>

                    <!-- Project Content Grid -->
                    <div class="portfolio-content-grid" style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; align-items: start;">
                        <!-- Left Column - Project Details -->
                        <div class="project-details">
                            <div class="project-section" style="margin-bottom: 2rem;">
                                <h3 style="font-size: 1.1rem; font-weight: 600; margin-bottom: 1rem; color: #333;">Project description</h3>
                                <p class="project-description" style="color: #666; line-height: 1.6; font-size: 0.95rem;">
                                    This is a comprehensive web development project that involved creating a modern, responsive website with advanced functionality. The project included both frontend and backend development, database design, and deployment.
                                </p>
                            </div>

                            <div class="project-section" style="margin-bottom: 2rem;">
                                <h3 style="font-size: 1.1rem; font-weight: 600; margin-bottom: 1rem; color: #333;">Skills and deliverables</h3>
                                <div class="skills-tags" style="display: flex; flex-wrap: wrap; gap: 0.5rem;">
                                    <span class="skill-tag" style="background: #e8f4fd; color: #0066cc; padding: 0.3rem 0.8rem; border-radius: 20px; font-size: 0.85rem; font-weight: 500;">Web Development</span>
                                    <span class="skill-tag" style="background: #e8f4fd; color: #0066cc; padding: 0.3rem 0.8rem; border-radius: 20px; font-size: 0.85rem; font-weight: 500;">React.js</span>
                                    <span class="skill-tag" style="background: #e8f4fd; color: #0066cc; padding: 0.3rem 0.8rem; border-radius: 20px; font-size: 0.85rem; font-weight: 500;">Node.js</span>
                                    <span class="skill-tag" style="background: #e8f4fd; color: #0066cc; padding: 0.3rem 0.8rem; border-radius: 20px; font-size: 0.85rem; font-weight: 500;">Database Design</span>
                                </div>
                            </div>

                            <div class="project-actions" style="margin-top: 2rem;">
                                <button class="report-btn" style="background: none; border: none; color: #666; font-size: 0.9rem; cursor: pointer; text-decoration: underline;">
                                    Report an issue
                                </button>
                            </div>
                        </div>

                        <!-- Right Column - Project Image -->
                        <div class="project-image-container">
                            <div class="project-image" style="width: 100%; height: 300px; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 12px rgba(0,0,0,0.1);">
                                <img id="portfolioViewImage" src="https://via.placeholder.com/400x300?text=Project+Image" alt="Project Image" style="width: 100%; height: 100%; object-fit: cover;">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for Certification Edit -->
    <div class="modal" id="certificationEditModal">
        <div class="modal-content">
            <!-- Header Section -->
            <header class="modal-header">
                <div class="modal-header-content">
                    <div class="logo">
                        <div class="logo-image">
                            <img src="{{ url_for('static', filename='img/logo.png') }}" alt="GigGenius Logo">
                        </div>
                        <span class="logo-text">GigGenius</span>
                    </div>
                </div>
            </header>

            <!-- Form Section -->
            <div class="modal-body">
                <h2 class="modal-title">Edit Certification *</h2>
                <textarea class="modal-textarea" placeholder="Edit your certification details..."></textarea>
                <div class="modal-buttons">
                    <button class="modal-back-btn" id="certificationBackBtn">Back</button>
                    <button class="modal-next-btn">Save Changes</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for Portfolio Add (Upwork Style) -->
    <div class="modal" id="portfolioAddModal">
        <div class="modal-content" style="max-width: 800px;">
            <!-- Header Section -->
            <header class="modal-header">
                <div class="modal-header-content" style="justify-content: space-between;">
                    <h2 style="margin: 0; font-size: 1.5rem; font-weight: 600; color: #004AAD;">Add a new portfolio project</h2>
                    <button class="modal-close-btn" id="portfolioAddCloseBtn" style="background: none; border: none; font-size: 1.5rem; cursor: pointer; color: #666;">×</button>
                </div>
            </header>

            <!-- Form Section -->
            <div class="modal-body" style="padding: 2rem;">
                <p style="color: #666; margin-bottom: 2rem; font-size: 0.9rem;">All fields are required unless otherwise indicated</p>

                <div class="upwork-form">
                    <!-- Project Title -->
                    <div class="form-group" style="margin-bottom: 2rem;">
                        <label for="projectTitle" style="display: block; font-weight: 600; margin-bottom: 0.5rem; color: #333;">Project title</label>
                        <input type="text" id="projectTitle" class="form-control" placeholder="Enter a brief but descriptive title" style="width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 6px; font-size: 1rem;">
                        <div style="text-align: right; font-size: 0.8rem; color: #666; margin-top: 0.25rem;">70 characters left</div>
                    </div>

                    <!-- Your Role -->
                    <div class="form-group" style="margin-bottom: 2rem;">
                        <label for="projectRole" style="display: block; font-weight: 600; margin-bottom: 0.5rem; color: #333;">Your role <span style="color: #666; font-weight: normal;">(optional)</span></label>
                        <input type="text" id="projectRole" class="form-control" placeholder="e.g., Front-end engineer or Marketing analyst" style="width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 6px; font-size: 1rem;">
                        <div style="text-align: right; font-size: 0.8rem; color: #666; margin-top: 0.25rem;">100 characters left</div>
                    </div>

                    <!-- Project Description -->
                    <div class="form-group" style="margin-bottom: 2rem;">
                        <label for="projectDescription" style="display: block; font-weight: 600; margin-bottom: 0.5rem; color: #333;">Project description</label>
                        <div style="border: 2px dashed #004AAD; border-radius: 8px; padding: 3rem; text-align: center; background: #f8f9fa; position: relative;">
                            <div style="display: flex; justify-content: center; gap: 1rem; margin-bottom: 1rem;">
                                <button type="button" id="uploadImageBtn" title="Upload Image" style="width: 40px; height: 40px; border-radius: 50%; border: 2px solid #004AAD; background: white; color: #004AAD; cursor: pointer; display: flex; align-items: center; justify-content: center;">
                                    <i class="fas fa-image"></i>
                                </button>
                                <button type="button" id="uploadVideoBtn" title="Upload Video" style="width: 40px; height: 40px; border-radius: 50%; border: 2px solid #004AAD; background: white; color: #004AAD; cursor: pointer; display: flex; align-items: center; justify-content: center;">
                                    <i class="fas fa-video"></i>
                                </button>
                                <button type="button" id="addTextBlockBtn" title="Add Text Block" style="width: 40px; height: 40px; border-radius: 50%; border: 2px solid #004AAD; background: white; color: #004AAD; cursor: pointer; display: flex; align-items: center; justify-content: center;">
                                    <i class="fas fa-font"></i>
                                </button>
                                <button type="button" id="addLinkBtn" title="Add Link" style="width: 40px; height: 40px; border-radius: 50%; border: 2px solid #004AAD; background: white; color: #004AAD; cursor: pointer; display: flex; align-items: center; justify-content: center;">
                                    <i class="fas fa-link"></i>
                                </button>
                                <button type="button" id="uploadPdfBtn" title="Upload PDF" style="width: 40px; height: 40px; border-radius: 50%; border: 2px solid #004AAD; background: white; color: #004AAD; cursor: pointer; display: flex; align-items: center; justify-content: center;">
                                    <i class="fas fa-file-pdf"></i>
                                </button>
                                <button type="button" id="addAudioBtn" title="Add Audio" style="width: 40px; height: 40px; border-radius: 50%; border: 2px solid #004AAD; background: white; color: #004AAD; cursor: pointer; display: flex; align-items: center; justify-content: center;">
                                    <i class="fas fa-microphone"></i>
                                </button>
                            </div>
                            <p style="color: #666; margin: 0; font-size: 1rem;">Add content</p>
                            <!-- Content will be dynamically added here -->
                            <div id="portfolioContent" style="margin-top: 1rem; text-align: left;"></div>
                        </div>
                        <div style="text-align: right; font-size: 0.8rem; color: #666; margin-top: 0.25rem;">600 characters left</div>
                    </div>

                    <!-- Skills and Deliverables -->
                    <div class="form-group" style="margin-bottom: 2rem;">
                        <label for="projectSkills" style="display: block; font-weight: 600; margin-bottom: 0.5rem; color: #333;">Skills and deliverables</label>
                        <input type="text" id="projectSkills" class="form-control" placeholder="Type to add skills relevant to this project" style="width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 6px; font-size: 1rem;">
                        <div style="text-align: right; font-size: 0.8rem; color: #666; margin-top: 0.25rem;">5 skills left</div>
                    </div>

                    <!-- Related Upwork Job -->
                    <div class="form-group" style="margin-bottom: 2rem;">
                        <label for="relatedJob" style="display: block; font-weight: 600; margin-bottom: 0.5rem; color: #333;">Related GigGenius job <span style="color: #666; font-weight: normal;">(optional)</span></label>
                        <input type="text" id="relatedJob" class="form-control" placeholder="Search a related job" style="width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 6px; font-size: 1rem;">
                    </div>
                </div>

                <div class="modal-buttons" style="display: flex; justify-content: flex-end; gap: 1rem; margin-top: 2rem; padding-top: 2rem; border-top: 1px solid #eee;">
                    <button class="modal-back-btn" id="portfolioAddBackBtn" style="background: none; border: 1px solid #ddd; color: #666; padding: 0.75rem 1.5rem; border-radius: 6px; cursor: pointer;">Save as draft</button>
                    <button class="modal-next-btn" id="portfolioAddSaveBtn" style="background: #CD208B; color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 6px; cursor: pointer; font-weight: 600;">Next: Preview</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for Portfolio Reorder -->
    <div class="modal" id="portfolioReorderModal">
        <div class="modal-content" style="max-width: 600px;">
            <!-- Header Section -->
            <header class="modal-header">
                <div class="modal-header-content" style="justify-content: space-between;">
                    <h2 style="margin: 0; font-size: 1.5rem; font-weight: 600;">Reorder portfolio projects</h2>
                    <button class="modal-close-btn" id="portfolioReorderCloseBtn" style="background: none; border: none; font-size: 1.5rem; cursor: pointer; color: #666;">×</button>
                </div>
            </header>

            <!-- Reorder Section -->
            <div class="modal-body" style="padding: 2rem;">
                <div class="reorder-list">
                    <!-- Portfolio Item 1 -->
                    <div class="reorder-item" style="display: flex; align-items: center; padding: 1rem; border: 1px solid #eee; border-radius: 8px; margin-bottom: 1rem; background: white;">
                        <div class="drag-handle" style="margin-right: 1rem; cursor: grab; color: #999;">
                            <i class="fas fa-grip-vertical"></i>
                        </div>
                        <div class="project-thumbnail" style="width: 60px; height: 40px; border-radius: 4px; overflow: hidden; margin-right: 1rem;">
                            <img src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400&h=250&fit=crop" alt="Project" style="width: 100%; height: 100%; object-fit: cover;">
                        </div>
                        <div class="project-info" style="flex: 1;">
                            <h4 style="margin: 0; font-size: 1rem; font-weight: 600; color: #333;">Bryan's Handyman & Construction LLC</h4>
                        </div>
                        <div class="reorder-controls" style="display: flex; flex-direction: column; gap: 0.25rem;">
                            <button class="move-up-btn" style="background: none; border: none; color: #666; cursor: pointer; padding: 0.25rem;">
                                <i class="fas fa-chevron-up"></i>
                            </button>
                            <button class="move-down-btn" style="background: none; border: none; color: #666; cursor: pointer; padding: 0.25rem;">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Portfolio Item 2 -->
                    <div class="reorder-item" style="display: flex; align-items: center; padding: 1rem; border: 1px solid #eee; border-radius: 8px; margin-bottom: 1rem; background: white;">
                        <div class="drag-handle" style="margin-right: 1rem; cursor: grab; color: #999;">
                            <i class="fas fa-grip-vertical"></i>
                        </div>
                        <div class="project-thumbnail" style="width: 60px; height: 40px; border-radius: 4px; overflow: hidden; margin-right: 1rem;">
                            <img src="https://images.unsplash.com/photo-1503676260728-1c00da094a0b?w=400&h=250&fit=crop" alt="Project" style="width: 100%; height: 100%; object-fit: cover;">
                        </div>
                        <div class="project-info" style="flex: 1;">
                            <h4 style="margin: 0; font-size: 1rem; font-weight: 600; color: #333;">Virtual Assistant Services</h4>
                        </div>
                        <div class="reorder-controls" style="display: flex; flex-direction: column; gap: 0.25rem;">
                            <button class="move-up-btn" style="background: none; border: none; color: #666; cursor: pointer; padding: 0.25rem;">
                                <i class="fas fa-chevron-up"></i>
                            </button>
                            <button class="move-down-btn" style="background: none; border: none; color: #666; cursor: pointer; padding: 0.25rem;">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Portfolio Item 3 -->
                    <div class="reorder-item" style="display: flex; align-items: center; padding: 1rem; border: 1px solid #eee; border-radius: 8px; margin-bottom: 1rem; background: white;">
                        <div class="drag-handle" style="margin-right: 1rem; cursor: grab; color: #999;">
                            <i class="fas fa-grip-vertical"></i>
                        </div>
                        <div class="project-thumbnail" style="width: 60px; height: 40px; border-radius: 4px; overflow: hidden; margin-right: 1rem;">
                            <img src="https://images.unsplash.com/photo-1561070791-2526d30994b5?w=400&h=250&fit=crop" alt="Project" style="width: 100%; height: 100%; object-fit: cover;">
                        </div>
                        <div class="project-info" style="flex: 1;">
                            <h4 style="margin: 0; font-size: 1rem; font-weight: 600; color: #333;">Graphics Design and Web Development</h4>
                        </div>
                        <div class="reorder-controls" style="display: flex; flex-direction: column; gap: 0.25rem;">
                            <button class="move-up-btn" style="background: none; border: none; color: #666; cursor: pointer; padding: 0.25rem;">
                                <i class="fas fa-chevron-up"></i>
                            </button>
                            <button class="move-down-btn" style="background: none; border: none; color: #666; cursor: pointer; padding: 0.25rem;">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Portfolio Item 4 -->
                    <div class="reorder-item" style="display: flex; align-items: center; padding: 1rem; border: 1px solid #eee; border-radius: 8px; margin-bottom: 1rem; background: white;">
                        <div class="drag-handle" style="margin-right: 1rem; cursor: grab; color: #999;">
                            <i class="fas fa-grip-vertical"></i>
                        </div>
                        <div class="project-thumbnail" style="width: 60px; height: 40px; border-radius: 4px; overflow: hidden; margin-right: 1rem;">
                            <img src="https://images.unsplash.com/photo-1551650975-87deedd944c3?w=400&h=250&fit=crop" alt="Project" style="width: 100%; height: 100%; object-fit: cover;">
                        </div>
                        <div class="project-info" style="flex: 1;">
                            <h4 style="margin: 0; font-size: 1rem; font-weight: 600; color: #333;">GigGenius Web and App Development</h4>
                            <div style="background: #28a745; color: white; padding: 0.2rem 0.5rem; border-radius: 4px; font-size: 0.75rem; display: inline-block; margin-top: 0.25rem;">Reorder portfolio projects</div>
                        </div>
                        <div class="reorder-controls" style="display: flex; flex-direction: column; gap: 0.25rem;">
                            <button class="move-up-btn" style="background: none; border: none; color: #666; cursor: pointer; padding: 0.25rem;">
                                <i class="fas fa-chevron-up"></i>
                            </button>
                            <button class="move-down-btn" style="background: none; border: none; color: #666; cursor: pointer; padding: 0.25rem;">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Portfolio Item 5 -->
                    <div class="reorder-item" style="display: flex; align-items: center; padding: 1rem; border: 1px solid #eee; border-radius: 8px; margin-bottom: 1rem; background: white;">
                        <div class="drag-handle" style="margin-right: 1rem; cursor: grab; color: #999;">
                            <i class="fas fa-grip-vertical"></i>
                        </div>
                        <div class="project-thumbnail" style="width: 60px; height: 40px; border-radius: 4px; overflow: hidden; margin-right: 1rem;">
                            <img src="https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=250&fit=crop" alt="Project" style="width: 100%; height: 100%; object-fit: cover;">
                        </div>
                        <div class="project-info" style="flex: 1;">
                            <h4 style="margin: 0; font-size: 1rem; font-weight: 600; color: #333;">Bryan's Handyman and Construction LLC</h4>
                            <div style="color: #666; font-size: 0.875rem;">April 2024</div>
                        </div>
                        <div class="reorder-controls" style="display: flex; flex-direction: column; gap: 0.25rem;">
                            <button class="move-up-btn" style="background: none; border: none; color: #666; cursor: pointer; padding: 0.25rem;">
                                <i class="fas fa-chevron-up"></i>
                            </button>
                            <button class="move-down-btn" style="background: none; border: none; color: #666; cursor: pointer; padding: 0.25rem;">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Portfolio Item 6 -->
                    <div class="reorder-item" style="display: flex; align-items: center; padding: 1rem; border: 1px solid #eee; border-radius: 8px; margin-bottom: 1rem; background: white;">
                        <div class="drag-handle" style="margin-right: 1rem; cursor: grab; color: #999;">
                            <i class="fas fa-grip-vertical"></i>
                        </div>
                        <div class="project-thumbnail" style="width: 60px; height: 40px; border-radius: 4px; overflow: hidden; margin-right: 1rem;">
                            <img src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=250&fit=crop" alt="Project" style="width: 100%; height: 100%; object-fit: cover;">
                        </div>
                        <div class="project-info" style="flex: 1;">
                            <h4 style="margin: 0; font-size: 1rem; font-weight: 600; color: #333;">SMB Paralegal Services</h4>
                            <div style="color: #666; font-size: 0.875rem;">April 2024</div>
                        </div>
                        <div class="reorder-controls" style="display: flex; flex-direction: column; gap: 0.25rem;">
                            <button class="move-up-btn" style="background: none; border: none; color: #666; cursor: pointer; padding: 0.25rem;">
                                <i class="fas fa-chevron-up"></i>
                            </button>
                            <button class="move-down-btn" style="background: none; border: none; color: #666; cursor: pointer; padding: 0.25rem;">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="modal-buttons" style="display: flex; justify-content: flex-end; gap: 1rem; margin-top: 2rem; padding-top: 2rem; border-top: 1px solid #eee;">
                    <button class="modal-back-btn" id="portfolioReorderCancelBtn" style="background: none; border: 1px solid #ddd; color: #666; padding: 0.75rem 1.5rem; border-radius: 6px; cursor: pointer;">Cancel</button>
                    <button class="modal-next-btn" id="portfolioReorderSaveBtn" style="background: #28a745; color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 6px; cursor: pointer; font-weight: 600;">Save</button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // Side Notification Functions
        function showSideNotification(message = 'Professional summary has been updated') {
            const notification = document.getElementById('sideNotification');
            const messageElement = document.getElementById('notificationMessage');

            if (notification && messageElement) {
                messageElement.textContent = message;
                notification.classList.add('show');

                // Auto hide after 4 seconds
                setTimeout(() => {
                    hideSideNotification();
                }, 4000);
            }
        }

        function hideSideNotification() {
            const notification = document.getElementById('sideNotification');
            if (notification) {
                notification.classList.remove('show');
            }
        }

        // Close button functionality
        document.addEventListener('DOMContentLoaded', function() {
            const closeBtn = document.getElementById('notificationClose');
            if (closeBtn) {
                closeBtn.addEventListener('click', hideSideNotification);
            }
        });
    </script>
    <script>
        // Show More functionality for summary text
        const summaryText = document.getElementById('summaryText');
        const showMoreBtn = document.getElementById('showMore');
        const editSummaryBtn = document.getElementById('editSummaryBtn');

        showMoreBtn.addEventListener('click', () => {
            summaryText.textContent = "Collapsible text is perfect for longer content like paragraphs and descriptions. It's a great way to give people more information while keeping your layout clean.";
            showMoreBtn.style.display = 'none';
            // Edit button is already visible
        });

        // Video functionality
        const videoContainer = document.getElementById('videoContainer');
        const videoPlaceholder = document.getElementById('videoPlaceholder');
        const videoPlayer = document.getElementById('videoPlayer');
        const uploadVideoBtn = document.getElementById('uploadVideoBtn');
        const videoUpload = document.getElementById('videoUpload');
        const deleteVideoBtn = document.getElementById('deleteVideoBtn');
        const replaceVideoBtn = document.getElementById('replaceVideoBtn');
        const expandBtn = document.getElementById('expandBtn');
        const video = document.getElementById('profileVideo');

        // Check if there's an existing video (you can modify this logic based on your data)
        let hasVideo = false; // Set this to true if user has uploaded video

        // Show appropriate view based on video existence
        function updateVideoView() {
            if (hasVideo) {
                videoPlaceholder.style.display = 'none';
                videoPlayer.style.display = 'block';
            } else {
                videoPlaceholder.style.display = 'flex';
                videoPlayer.style.display = 'none';
            }
        }

        // Initialize video view
        updateVideoView();

        // Upload video functionality
        if (uploadVideoBtn && videoUpload) {
            uploadVideoBtn.addEventListener('click', () => {
                videoUpload.click();
            });

            videoUpload.addEventListener('change', (event) => {
                const file = event.target.files[0];
                if (file && file.type.startsWith('video/')) {
                    const videoURL = URL.createObjectURL(file);
                    video.src = videoURL;
                    hasVideo = true;
                    updateVideoView();
                    console.log('Video uploaded:', file.name);
                    // Here you would typically upload the file to your server
                } else {
                    alert('Please select a valid video file.');
                }
            });
        }

        // Replace video functionality
        if (replaceVideoBtn) {
            replaceVideoBtn.addEventListener('click', () => {
                videoUpload.click();
            });
        }

        // Delete video functionality
        if (deleteVideoBtn) {
            deleteVideoBtn.addEventListener('click', () => {
                if (confirm('Are you sure you want to delete this video?')) {
                    video.src = '';
                    hasVideo = false;
                    updateVideoView();
                    console.log('Video deleted');
                    // Here you would typically delete the file from your server
                }
            });
        }

        // Video expand/collapse functionality
        if (expandBtn) {
            expandBtn.addEventListener('click', () => {
                videoContainer.classList.toggle('expanded');
                if (videoContainer.classList.contains('expanded')) {
                    expandBtn.innerHTML = '<i class="fas fa-compress"></i> Exit Fullscreen';
                    document.body.style.overflow = 'hidden';
                } else {
                    expandBtn.innerHTML = '<i class="fas fa-expand"></i> Fullscreen';
                    document.body.style.overflow = '';
                }
            });
        }

        // Close expanded video when clicking outside
        document.addEventListener('click', (event) => {
            if (videoContainer && videoContainer.classList.contains('expanded') &&
                !videoPlayer.contains(event.target) &&
                !expandBtn.contains(event.target)) {
                videoContainer.classList.remove('expanded');
                if (expandBtn) {
                    expandBtn.innerHTML = '<i class="fas fa-expand"></i> Fullscreen';
                }
                document.body.style.overflow = '';
            }
        });

        // Close expanded video when pressing Escape key
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Escape' && videoContainer && videoContainer.classList.contains('expanded')) {
                videoContainer.classList.remove('expanded');
                if (expandBtn) {
                    expandBtn.innerHTML = '<i class="fas fa-expand"></i> Fullscreen';
                }
                document.body.style.overflow = '';
            }
        });

        // This section has been moved to the DOMContentLoaded event below to avoid conflicts

        // Mobile Menu Toggle
        const mobileMenuBtn = document.getElementById('mobileMenuBtn');
        const mobileMenu = document.getElementById('mobileMenu');

        if (mobileMenuBtn && mobileMenu) {
            mobileMenuBtn.addEventListener('click', () => {
                mobileMenu.classList.toggle('active');
                if (mobileMenu.classList.contains('active')) {
                    mobileMenuBtn.innerHTML = '<i class="fas fa-times"></i>';
                } else {
                    mobileMenuBtn.innerHTML = '<i class="fas fa-bars"></i>';
                }
            });

            // Close mobile menu when clicking outside
            document.addEventListener('click', (event) => {
                if (!mobileMenuBtn.contains(event.target) && !mobileMenu.contains(event.target)) {
                    mobileMenu.classList.remove('active');
                    mobileMenuBtn.innerHTML = '<i class="fas fa-bars"></i>';
                }
            });
        }

        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM Content Loaded - Initializing modals...');

            // Debug: Log the genius data
            console.log('Genius country value:', '{{ genius.country }}');
            console.log('Genius data:', {
                country: '{{ genius.country }}',
                availability: '{{ genius.availability }}',
                expertise: '{{ genius.expertise }}',
                position: '{{ genius.position }}'
            });

            // Modal functionality
            const professionalSummaryModal = document.getElementById('professionalSummaryModal');
            const introductionModal = document.getElementById('introductionModal');
            const editProfileModal = document.getElementById('editProfileModal');
            const portfolioViewModal = document.getElementById('portfolioViewModal');
            const portfolioAddModal = document.getElementById('portfolioAddModal');
            const portfolioReorderModal = document.getElementById('portfolioReorderModal');
            const certificationEditModal = document.getElementById('certificationEditModal');

            // Edit buttons
            const editSummary = document.getElementById('editSummaryBtn');
            const editIntroduction = document.getElementById('editIntroductionBtn');
            const editProfile = document.getElementById('editProfileBtn');
            const portfolioAdd = document.getElementById('portfolioAddBtn');
            const portfolioRefresh = document.querySelector('.portfolio-refresh-btn');
            const portfolioViewCloseBtn = document.getElementById('portfolioViewCloseBtn');
            const portfolioAddCloseBtn = document.getElementById('portfolioAddCloseBtn');
            const portfolioReorderCloseBtn = document.getElementById('portfolioReorderCloseBtn');
            const certificationEdit1 = document.getElementById('certificationEditBtn1');
            const certificationEdit2 = document.getElementById('certificationEditBtn2');

            // Back buttons
            const summaryBackBtn = document.getElementById('summaryBackBtn');
            const introBackBtn = document.getElementById('introBackBtn');
            const profileBackBtn = document.getElementById('profileBackBtn');
            const portfolioAddBackBtn = document.getElementById('portfolioAddBackBtn');
            const portfolioReorderCancelBtn = document.getElementById('portfolioReorderCancelBtn');
            const certificationBackBtn = document.getElementById('certificationBackBtn');

            console.log('Modal elements found:', {
                professionalSummaryModal: !!professionalSummaryModal,
                editSummary: !!editSummary,
                editIntroduction: !!editIntroduction,
                editProfile: !!editProfile
            });

            // Close modals function
            function closeModal(modal) {
                if (modal) {
                    modal.classList.remove('active');
                    document.body.style.overflow = '';
                }
            }

            // Open modals - with null checks and debugging
            if (editSummary && professionalSummaryModal) {
                editSummary.addEventListener('click', (e) => {
                    e.preventDefault();
                    console.log('Edit Summary clicked');

                    // Load current text into textarea when opening modal
                    const summaryText = document.getElementById('summaryText');
                    const professionalSummaryTextarea = document.getElementById('professionalSummaryTextarea');

                    if (summaryText && professionalSummaryTextarea) {
                        // Get the current text, excluding the placeholder text
                        const currentText = summaryText.textContent || summaryText.innerText || '';
                        const placeholderText = 'Add a professional summary to showcase your skills and experience to potential clients. Click the Edit button to get started.';

                        if (currentText.trim() === placeholderText.trim()) {
                            professionalSummaryTextarea.value = '';
                        } else {
                            professionalSummaryTextarea.value = currentText.trim();
                        }
                    }

                    professionalSummaryModal.classList.add('active');
                    document.body.style.overflow = 'hidden';
                });
            }

            if (editIntroduction && introductionModal) {
                editIntroduction.addEventListener('click', (e) => {
                    e.preventDefault();
                    console.log('Edit Introduction clicked');
                    introductionModal.classList.add('active');
                    document.body.style.overflow = 'hidden';
                });
            }

            if (editProfile && editProfileModal) {
                editProfile.addEventListener('click', async (e) => {
                    e.preventDefault();
                    console.log('Edit Profile clicked');

                    try {
                        // Fetch current profile data from database
                        const response = await fetch('/api/genius/profile_data');
                        const data = await response.json();

                        if (data.success && data.profile_data) {
                            const profileData = data.profile_data;

                            // Set form values with database data
                            const emailField = document.getElementById('email');
                            const mobileField = document.getElementById('mobile');
                            const positionField = document.getElementById('position');
                            const expertiseField = document.getElementById('expertise');
                            const rateField = document.getElementById('rate');
                            const availabilityField = document.getElementById('availability');
                            const countryField = document.getElementById('country');
                            const languageField = document.getElementById('language');

                            if (emailField) emailField.value = profileData.email || '';
                            if (mobileField) mobileField.value = profileData.mobile || '';
                            if (positionField) positionField.value = profileData.position || '';
                            if (expertiseField) expertiseField.value = profileData.expertise || 'Beginner';
                            if (rateField) rateField.value = profileData.hourly_rate || '';
                            if (availabilityField) availabilityField.value = profileData.availability || 'fulltime';
                            if (countryField) countryField.value = profileData.country || '';
                            if (languageField) languageField.value = profileData.language || 'English';
                        } else {
                            console.error('Failed to fetch profile data:', data.error);
                        }
                    } catch (error) {
                        console.error('Error fetching profile data:', error);
                    }

                    editProfileModal.classList.add('active');
                    document.body.style.overflow = 'hidden';
                });
            }

            if (portfolioAdd && portfolioAddModal) {
                portfolioAdd.addEventListener('click', (e) => {
                    e.preventDefault();
                    console.log('Portfolio Add clicked');
                    portfolioAddModal.classList.add('active');
                    document.body.style.overflow = 'hidden';

                    // Setup portfolio content button listeners when modal opens
                    setTimeout(() => {
                        setupPortfolioButtons();
                    }, 100);
                });
            }

            if (portfolioRefresh && portfolioReorderModal) {
                portfolioRefresh.addEventListener('click', (e) => {
                    e.preventDefault();
                    console.log('Portfolio Refresh clicked');
                    portfolioReorderModal.classList.add('active');
                    document.body.style.overflow = 'hidden';
                });
            }

            if (certificationEdit1 && certificationEditModal) {
                certificationEdit1.addEventListener('click', (e) => {
                    e.preventDefault();
                    console.log('Certification Edit 1 clicked');
                    certificationEditModal.classList.add('active');
                    document.body.style.overflow = 'hidden';
                });
            }

            if (certificationEdit2 && certificationEditModal) {
                certificationEdit2.addEventListener('click', (e) => {
                    e.preventDefault();
                    console.log('Certification Edit 2 clicked');
                    certificationEditModal.classList.add('active');
                    document.body.style.overflow = 'hidden';
                });
            }

            // Close modals with back buttons
            if (summaryBackBtn) {
                summaryBackBtn.addEventListener('click', () => {
                    console.log('Summary back button clicked');
                    closeModal(professionalSummaryModal);
                });
            }

            if (introBackBtn) {
                introBackBtn.addEventListener('click', () => {
                    console.log('Intro back button clicked');
                    closeModal(introductionModal);
                });
            }

            if (profileBackBtn) {
                profileBackBtn.addEventListener('click', () => {
                    console.log('Profile back button clicked');
                    closeModal(editProfileModal);
                });
            }

            if (portfolioViewCloseBtn) {
                portfolioViewCloseBtn.addEventListener('click', () => {
                    console.log('Portfolio view close button clicked');
                    closeModal(portfolioViewModal);
                });
            }

            if (portfolioAddCloseBtn) {
                portfolioAddCloseBtn.addEventListener('click', () => {
                    console.log('Portfolio add close button clicked');
                    closeModal(portfolioAddModal);
                });
            }

            if (portfolioAddBackBtn) {
                portfolioAddBackBtn.addEventListener('click', () => {
                    console.log('Portfolio add back button clicked');
                    closeModal(portfolioAddModal);
                });
            }

            if (portfolioReorderCloseBtn) {
                portfolioReorderCloseBtn.addEventListener('click', () => {
                    console.log('Portfolio reorder close button clicked');
                    closeModal(portfolioReorderModal);
                });
            }

            if (portfolioReorderCancelBtn) {
                portfolioReorderCancelBtn.addEventListener('click', () => {
                    console.log('Portfolio reorder cancel button clicked');
                    closeModal(portfolioReorderModal);
                });
            }

            if (certificationBackBtn) {
                certificationBackBtn.addEventListener('click', () => {
                    console.log('Certification back button clicked');
                    closeModal(certificationEditModal);
                });
            }

            // Close modals when clicking outside
            window.addEventListener('click', (event) => {
                if (event.target === professionalSummaryModal) {
                    closeModal(professionalSummaryModal);
                }
                if (event.target === introductionModal) {
                    closeModal(introductionModal);
                }
                if (event.target === editProfileModal) {
                    closeModal(editProfileModal);
                }
                if (event.target === portfolioViewModal) {
                    closeModal(portfolioViewModal);
                }
                if (event.target === portfolioAddModal) {
                    closeModal(portfolioAddModal);
                }
                if (event.target === portfolioReorderModal) {
                    closeModal(portfolioReorderModal);
                }
                if (event.target === certificationEditModal) {
                    closeModal(certificationEditModal);
                }
            });

            // Close modals with Escape key
            document.addEventListener('keydown', (event) => {
                if (event.key === 'Escape') {
                    closeModal(professionalSummaryModal);
                    closeModal(introductionModal);
                    closeModal(editProfileModal);
                    closeModal(portfolioViewModal);
                    closeModal(portfolioAddModal);
                    closeModal(portfolioReorderModal);
                    closeModal(certificationEditModal);
                }
            });

            // For mobile devices, make dropdown items clickable to expand
            const dropdownTriggers = document.querySelectorAll('.dropdown-trigger');

            dropdownTriggers.forEach(trigger => {
                trigger.addEventListener('click', function(e) {
                    if (window.innerWidth < 768) {
                        e.preventDefault();
                        const dropdown = this.nextElementSibling;
                        dropdown.style.display = dropdown.style.display === 'block' ? 'none' : 'block';
                    }
                });
            });

            // Close dropdowns when clicking outside
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.dropdown')) {
                    const dropdownMenus = document.querySelectorAll('.dropdown-menu');
                    dropdownMenus.forEach(menu => {
                        if (window.innerWidth < 768) {
                            menu.style.display = 'none';
                        }
                    });
                }

                // Close profile dropdown when clicking outside
                if (!e.target.closest('.profile-dropdown')) {
                    const profileDropdown = document.getElementById('profileDropdown');
                    if (profileDropdown) {
                        profileDropdown.classList.remove('active');
                    }
                }

                // Close notification dropdown when clicking outside
                if (!e.target.closest('.notification-dropdown')) {
                    const notificationDropdown = document.getElementById('notificationDropdown');
                    if (notificationDropdown) {
                        notificationDropdown.classList.remove('active');
                    }
                }
            });

            // Profile dropdown toggle
            const profileIcon = document.getElementById('profileIcon');
            const profileDropdown = document.getElementById('profileDropdown');

            if (profileIcon && profileDropdown) {
                profileIcon.addEventListener('click', function(e) {
                    e.stopPropagation();
                    profileDropdown.classList.toggle('active');

                    // Close notification dropdown if open
                    const notificationDropdown = document.getElementById('notificationDropdown');
                    if (notificationDropdown && notificationDropdown.classList.contains('active')) {
                        notificationDropdown.classList.remove('active');
                    }
                });

                profileDropdown.addEventListener('click', (event) => {
                    event.stopPropagation();
                });
            }

            // Notification dropdown toggle
            const notificationBtn = document.getElementById('notificationBtn');
            const notificationDropdown = document.getElementById('notificationDropdown');
            const markAllReadBtn = document.getElementById('markAllRead');

            if (notificationBtn && notificationDropdown) {
                notificationBtn.addEventListener('click', function(event) {
                    event.stopPropagation();
                    notificationDropdown.classList.toggle('active');

                    // Close profile dropdown if open
                    if (profileDropdown && profileDropdown.classList.contains('active')) {
                        profileDropdown.classList.remove('active');
                    }
                });

                notificationDropdown.addEventListener('click', (event) => {
                    event.stopPropagation();
                });

                // Mark all notifications as read
                if (markAllReadBtn) {
                    markAllReadBtn.addEventListener('click', function() {
                        const unreadNotifications = document.querySelectorAll('.notification-item.unread');
                        unreadNotifications.forEach(notification => {
                            notification.classList.remove('unread');
                        });

                        // Remove the notification indicator
                        const indicator = document.querySelector('.notification-indicator');
                        if (indicator) {
                            indicator.style.display = 'none';
                        }
                    });
                }

                // Mark individual notification as read when clicked
                const notificationItems = document.querySelectorAll('.notification-item');
                notificationItems.forEach(item => {
                    item.addEventListener('click', function() {
                        this.classList.remove('unread');

                        // Check if there are any unread notifications left
                        const unreadNotifications = document.querySelectorAll('.notification-item.unread');
                        if (unreadNotifications.length === 0) {
                            const indicator = document.querySelector('.notification-indicator');
                            if (indicator) {
                                indicator.style.display = 'none';
                            }
                        }
                    });
                });
            }

            // Professional Summary Next Button functionality
            const summaryNextBtn = document.getElementById('summaryNextBtn');
            const professionalSummaryTextarea = document.getElementById('professionalSummaryTextarea');

            if (summaryNextBtn && professionalSummaryTextarea) {
                summaryNextBtn.addEventListener('click', async function(e) {
                    e.preventDefault();

                    const professionalSummary = professionalSummaryTextarea.value.trim();

                    if (!professionalSummary) {
                        alert('Please enter a professional summary before saving.');
                        return;
                    }

                    // Show loading state
                    summaryNextBtn.disabled = true;
                    summaryNextBtn.textContent = 'Saving...';

                    try {
                        const response = await fetch('/update_professional_summary', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                professional_summary: professionalSummary
                            })
                        });

                        const data = await response.json();

                        if (data.success) {
                            // Update the display text
                            const summaryText = document.getElementById('summaryText');
                            if (summaryText) {
                                if (professionalSummary.trim()) {
                                    summaryText.innerHTML = professionalSummary;
                                } else {
                                    summaryText.innerHTML = '<span style="color: #6b7280; font-style: italic;">Add a professional summary to showcase your skills and experience to potential clients. Click the Edit button to get started.</span>';
                                }
                            }

                            // Close the modal
                            closeModal(professionalSummaryModal);

                            // Show side notification instead of alert
                            showSideNotification('Professional summary has been updated');
                        } else {
                            alert('Error: ' + (data.error || 'Failed to update professional summary'));
                        }
                    } catch (error) {
                        console.error('Error updating professional summary:', error);
                        alert('An error occurred while updating your professional summary. Please try again.');
                    } finally {
                        // Reset button state
                        summaryNextBtn.disabled = false;
                        summaryNextBtn.textContent = 'Next';
                    }
                });
            }

            // Introduction Next Button functionality
            const introNextBtn = document.getElementById('introNextBtn');
            const introductionTextarea = document.getElementById('introductionTextarea');

            if (introNextBtn && introductionTextarea) {
                introNextBtn.addEventListener('click', async function(e) {
                    e.preventDefault();

                    const introduction = introductionTextarea.value.trim();
                    console.log('Introduction content:', introduction);

                    if (!introduction) {
                        alert('Please enter an introduction before saving.');
                        return;
                    }

                    // Show loading state
                    introNextBtn.disabled = true;
                    introNextBtn.textContent = 'Saving...';

                    try {
                        console.log('Sending request to /introduction');
                        const response = await fetch('/introduction', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                introduction: introduction
                            })
                        });

                        console.log('Response status:', response.status);
                        const data = await response.json();
                        console.log('Response data:', data);

                        if (data.success) {
                            // Update the display text
                            const introductionDisplay = document.getElementById('introductionDisplay');
                            if (introductionDisplay) {
                                introductionDisplay.value = introduction;
                            }

                            // Close the modal
                            closeModal(introductionModal);

                            // Show side notification
                            showSideNotification('Introduction updated successfully!');
                        } else {
                            alert('Error: ' + (data.error || 'Failed to update introduction'));
                        }
                    } catch (error) {
                        console.error('Error updating introduction:', error);
                        alert('An error occurred while updating your introduction. Please try again.');
                    } finally {
                        // Reset button state
                        introNextBtn.disabled = false;
                        introNextBtn.textContent = 'Next';
                    }
                });
            }
            // Profile photo upload functionality
            const profileUpload = document.getElementById('profile-upload');
            const profilePhoto = document.querySelector('.profile-photo img');
            let selectedFile = null;

            if (profileUpload) {
                profileUpload.addEventListener('change', function(e) {
                    const file = e.target.files[0];
                    if (file) {
                        // Validate file type
                        if (!file.type.startsWith('image/')) {
                            alert('Please select an image file (JPEG, PNG, etc.)');
                            e.target.value = '';
                            return;
                        }

                        // Validate file size (2MB limit)
                        if (file.size > 2 * 1024 * 1024) {
                            alert('File size must be less than 2MB');
                            e.target.value = '';
                            return;
                        }

                        selectedFile = file;

                        // Preview the image
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            profilePhoto.src = e.target.result;
                        };
                        reader.readAsDataURL(file);
                    }
                });
            }

            // Profile save functionality
            const saveProfileBtn = document.getElementById('saveProfileBtn');
            if (saveProfileBtn) {
                saveProfileBtn.addEventListener('click', async (e) => {
                    e.preventDefault();
                    console.log('Edit Profile clicked - saving profile data');

                    // Get form values
                    const email = document.getElementById('email').value.trim();
                    const mobile = document.getElementById('mobile').value.trim();
                    const position = document.getElementById('position').value.trim();
                    const expertise = document.getElementById('expertise').value;
                    const hourly_rate = document.getElementById('rate').value;
                    const availability = document.getElementById('availability').value;
                    const country = document.getElementById('country').value;
                    const language = document.getElementById('language').value;

                    // Validate required fields
                    if (!email || !mobile || !position || !expertise || !hourly_rate || !availability || !country) {
                        alert('Please fill in all required fields');
                        return;
                    }

                    // Validate email format
                    const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
                    if (!emailPattern.test(email)) {
                        alert('Please enter a valid email address');
                        return;
                    }

                    // Validate hourly rate
                    if (isNaN(hourly_rate) || parseFloat(hourly_rate) < 0) {
                        alert('Please enter a valid hourly rate');
                        return;
                    }

                    try {
                        // Show loading state
                        saveProfileBtn.disabled = true;
                        saveProfileBtn.textContent = 'Saving...';

                        // Check if we have a profile photo to upload
                        if (selectedFile) {
                            // Use FormData for file upload
                            const formData = new FormData();
                            formData.append('email', email);
                            formData.append('mobile', mobile);
                            formData.append('position', position);
                            formData.append('expertise', expertise);
                            formData.append('hourly_rate', parseFloat(hourly_rate));
                            formData.append('availability', availability);
                            formData.append('country', country);
                            formData.append('language', language);
                            formData.append('profile_photo', selectedFile);

                            const response = await fetch('/update_genius_profile', {
                                method: 'POST',
                                body: formData
                            });

                            const data = await response.json();

                            if (data.success) {
                                // Update all display elements immediately
                                const displayPosition = document.getElementById('displayPosition');
                                const displayCountry = document.getElementById('displayCountry');
                                const profilePosition = document.getElementById('profilePosition');
                                const displayAvailability = document.getElementById('displayAvailability');
                                const displayLanguage = document.getElementById('displayLanguage');
                                const displayCountryField = document.getElementById('displayCountryField');
                                const hourlyRateElement = document.getElementById('hourlyRate');

                                if (displayPosition) displayPosition.textContent = position;
                                if (displayCountry) displayCountry.textContent = country;
                                if (profilePosition) profilePosition.textContent = position;
                                if (displayAvailability) {
                                    const displayText = availability === 'fulltime' ? 'Full-Time' : availability === 'parttime' ? 'Part-Time' : availability;
                                    displayAvailability.value = displayText;
                                }
                                if (displayLanguage) displayLanguage.value = language;
                                if (displayCountryField) displayCountryField.value = country;
                                if (hourlyRateElement) {
                                    if (hourly_rate && hourly_rate > 0) {
                                        hourlyRateElement.textContent = `$${hourly_rate}`;
                                    } else {
                                        hourlyRateElement.textContent = 'No rate set';
                                    }
                                }

                                showSideNotification('Profile updated successfully!');
                                closeModal(editProfileModal);
                                // Don't reload the page, just update the display
                            } else {
                                alert('Error: ' + (data.error || 'Failed to update profile'));
                            }
                        } else {
                            // Use JSON for regular data without file upload
                            const response = await fetch('/update_genius_profile', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                },
                                body: JSON.stringify({
                                    email: email,
                                    mobile: mobile,
                                    position: position,
                                    expertise: expertise,
                                    hourly_rate: parseFloat(hourly_rate),
                                    availability: availability,
                                    country: country,
                                    language: language
                                })
                            });

                            const data = await response.json();

                            if (data.success) {
                                // Update all display elements immediately
                                const displayPosition = document.getElementById('displayPosition');
                                const displayCountry = document.getElementById('displayCountry');
                                const profilePosition = document.getElementById('profilePosition');
                                const displayAvailability = document.getElementById('displayAvailability');
                                const displayLanguage = document.getElementById('displayLanguage');
                                const displayCountryField = document.getElementById('displayCountryField');
                                const hourlyRateElement = document.getElementById('hourlyRate');

                                if (displayPosition) displayPosition.textContent = position;
                                if (displayCountry) displayCountry.textContent = country;
                                if (profilePosition) profilePosition.textContent = position;
                                if (displayAvailability) {
                                    const displayText = availability === 'fulltime' ? 'Full-Time' : availability === 'parttime' ? 'Part-Time' : availability;
                                    displayAvailability.value = displayText;
                                }
                                if (displayLanguage) displayLanguage.value = language;
                                if (displayCountryField) displayCountryField.value = country;
                                if (hourlyRateElement) {
                                    if (hourly_rate && hourly_rate > 0) {
                                        hourlyRateElement.textContent = `$${hourly_rate}`;
                                    } else {
                                        hourlyRateElement.textContent = 'No rate set';
                                    }
                                }

                                showSideNotification('Profile updated successfully!');
                                closeModal(editProfileModal);
                                // Don't reload the page, just update the display
                            } else {
                                alert('Error: ' + (data.error || 'Failed to update profile'));
                            }
                        }
                    } catch (error) {
                        alert('An error occurred while saving your profile');
                        console.error('Profile update error:', error);
                    } finally {
                        saveProfileBtn.disabled = false;
                        saveProfileBtn.textContent = 'Next';
                    }
                });
            }

            // Portfolio image upload functionality
            const portfolioUpload = document.getElementById('portfolio-upload');
            const portfolioPreview = document.getElementById('portfolioPreview');
            let selectedPortfolioFile = null;

            if (portfolioUpload) {
                portfolioUpload.addEventListener('change', function(e) {
                    const file = e.target.files[0];
                    if (file) {
                        // Validate file type
                        if (!file.type.startsWith('image/')) {
                            alert('Please select an image file (JPEG, PNG, etc.)');
                            e.target.value = '';
                            return;
                        }

                        // Validate file size (2MB limit)
                        if (file.size > 2 * 1024 * 1024) {
                            alert('File size must be less than 2MB');
                            e.target.value = '';
                            return;
                        }

                        selectedPortfolioFile = file;

                        // Preview the image
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            portfolioPreview.src = e.target.result;
                        };
                        reader.readAsDataURL(file);
                    }
                });
            }

            // Portfolio save functionality
            const portfolioAddSaveBtn = document.getElementById('portfolioAddSaveBtn');
            console.log('Portfolio save button found:', portfolioAddSaveBtn);
            if (portfolioAddSaveBtn) {
                portfolioAddSaveBtn.addEventListener('click', async (e) => {
                    e.preventDefault();
                    console.log('Portfolio Next: Preview clicked');
                    alert('Button clicked! Processing...');

                    // Get form values
                    const projectTitle = document.getElementById('projectTitle').value.trim();
                    const projectRole = document.getElementById('projectRole').value.trim();
                    const projectDescription = document.getElementById('portfolioContent').innerHTML.trim();

                    // Validate required fields
                    if (!projectTitle) {
                        alert('Please enter a project title');
                        return;
                    }

                    try {
                        // Show loading state
                        portfolioAddSaveBtn.disabled = true;
                        portfolioAddSaveBtn.textContent = 'Saving...';

                        console.log('Saving project and publishing - Title:', projectTitle, 'Role:', projectRole, 'Description:', projectDescription);

                        // Send request to save portfolio title as published
                        const response = await fetch('/save_portfolio_title', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                project_title: projectTitle,
                                project_role: projectRole,
                                project_description: projectDescription,
                                action: 'published'
                            })
                        });

                        console.log('Response status:', response.status);
                        const data = await response.json();
                        console.log('Response data:', data);

                        if (data.success) {
                            alert('Project title saved and published successfully!');

                            // Reset form
                            document.getElementById('projectTitle').value = '';
                            document.getElementById('projectRole').value = '';
                            document.getElementById('projectSkills').value = '';
                            document.getElementById('relatedJob').value = '';
                            document.getElementById('portfolioContent').innerHTML = '';
                            if (portfolioPreview) {
                                portfolioPreview.src = 'https://via.placeholder.com/150x150?text=Project+Image';
                            }
                            if (portfolioUpload) {
                                portfolioUpload.value = '';
                            }
                            selectedPortfolioFile = null;

                            closeModal(portfolioAddModal);

                            // Navigate to Published section and reload page
                            if (data.redirect === 'published') {
                                // Switch to published tab
                                const publishedTab = document.querySelector('[data-tab="published"]');
                                if (publishedTab) {
                                    publishedTab.click();
                                }
                            }

                            // Reload the page to show updated project title
                            window.location.reload();
                        } else {
                            alert('Error: ' + (data.error || 'Failed to save and publish project title'));
                        }
                    } catch (error) {
                        alert('An error occurred while saving your project title');
                        console.error('Portfolio save error:', error);
                    } finally {
                        portfolioAddSaveBtn.disabled = false;
                        portfolioAddSaveBtn.textContent = 'Next: Preview';
                    }
                });
            }



            // Portfolio Tabs functionality
            const portfolioTabs = document.querySelectorAll('.portfolio-tab');
            const publishedContent = document.getElementById('publishedContent');
            const draftsContent = document.getElementById('draftsContent');

            portfolioTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    // Remove active class from all tabs
                    portfolioTabs.forEach(t => t.classList.remove('active'));
                    // Add active class to clicked tab
                    this.classList.add('active');

                    // Show/hide different portfolio content
                    const tabType = this.getAttribute('data-tab');
                    console.log('Switched to tab:', tabType);

                    if (tabType === 'published') {
                        publishedContent.style.display = 'block';
                        draftsContent.style.display = 'none';
                    } else if (tabType === 'drafts') {
                        publishedContent.style.display = 'none';
                        draftsContent.style.display = 'block';
                    }
                });
            });

            // Portfolio Pagination functionality
            const paginationBtns = document.querySelectorAll('.pagination-btn');
            paginationBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    if (!this.classList.contains('prev') && !this.classList.contains('next')) {
                        // Remove active class from all pagination buttons
                        paginationBtns.forEach(b => b.classList.remove('active'));
                        // Add active class to clicked button
                        this.classList.add('active');
                    }
                });
            });

            // Portfolio Draft Save functionality
            if (portfolioAddBackBtn) {
                portfolioAddBackBtn.addEventListener('click', async (e) => {
                    e.preventDefault();
                    console.log('Portfolio Save as Draft clicked');

                    // Get form values
                    const projectTitle = document.getElementById('projectTitle').value.trim();
                    const projectRole = document.getElementById('projectRole').value.trim();
                    const projectDescription = document.getElementById('portfolioContent').innerHTML.trim();

                    // Validate required fields (only title required for draft)
                    if (!projectTitle) {
                        alert('Please enter a project title to save as draft');
                        return;
                    }

                    try {
                        // Show loading state
                        portfolioAddBackBtn.disabled = true;
                        portfolioAddBackBtn.textContent = 'Saving...';

                        console.log('Saving project as draft - Title:', projectTitle, 'Role:', projectRole, 'Description:', projectDescription);

                        // Send request to save portfolio title
                        const response = await fetch('/save_portfolio_title', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                project_title: projectTitle,
                                project_role: projectRole,
                                project_description: projectDescription,
                                action: 'draft'
                            })
                        });

                        console.log('Draft response status:', response.status);
                        const data = await response.json();
                        console.log('Draft response data:', data);

                        if (data.success) {
                            alert('Project title saved as draft successfully!');

                            // Reset form
                            document.getElementById('projectTitle').value = '';
                            document.getElementById('projectRole').value = '';
                            document.getElementById('projectSkills').value = '';
                            document.getElementById('relatedJob').value = '';
                            document.getElementById('portfolioContent').innerHTML = '';
                            if (portfolioPreview) {
                                portfolioPreview.src = 'https://via.placeholder.com/150x150?text=Project+Image';
                            }
                            if (portfolioUpload) {
                                portfolioUpload.value = '';
                            }
                            selectedPortfolioFile = null;

                            closeModal(portfolioAddModal);

                            // Navigate to Drafts section and reload page
                            if (data.redirect === 'drafts') {
                                // Switch to drafts tab
                                const draftsTab = document.querySelector('[data-tab="drafts"]');
                                if (draftsTab) {
                                    draftsTab.click();
                                }
                            }

                            // Reload the page to show updated project title
                            window.location.reload();
                        } else {
                            alert('Error: ' + (data.error || 'Failed to save project title as draft'));
                        }
                    } catch (error) {
                        alert('An error occurred while saving your project title as draft');
                        console.error('Portfolio draft save error:', error);
                    } finally {
                        portfolioAddBackBtn.disabled = false;
                        portfolioAddBackBtn.textContent = 'Save as draft';
                    }
                });
            }

            // Portfolio View/Publish functionality
            const publishPortfolioBtns = document.querySelectorAll('.publish-portfolio-btn');
            publishPortfolioBtns.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    const portfolioCard = this.closest('.portfolio-card');
                    const projectTitle = portfolioCard.getAttribute('data-project-title');
                    const projectRole = portfolioCard.getAttribute('data-project-role');
                    const projectDescription = portfolioCard.getAttribute('data-project-description');
                    const projectSkills = portfolioCard.getAttribute('data-project-skills');
                    const projectImage = portfolioCard.getAttribute('data-project-image');

                    // Update modal content
                    const modalTitle = portfolioViewModal.querySelector('.project-title');
                    const modalRole = portfolioViewModal.querySelector('.project-role strong');
                    const modalDescription = portfolioViewModal.querySelector('.project-description');
                    const modalImage = portfolioViewModal.querySelector('#portfolioViewImage');
                    const modalSkillsContainer = portfolioViewModal.querySelector('.skills-tags');

                    if (modalTitle) modalTitle.textContent = projectTitle;
                    if (modalRole) modalRole.textContent = projectRole;
                    if (modalDescription) modalDescription.textContent = projectDescription;
                    if (modalImage) modalImage.src = projectImage;

                    // Update skills
                    if (modalSkillsContainer && projectSkills) {
                        modalSkillsContainer.innerHTML = '';
                        const skills = projectSkills.split(',');
                        skills.forEach(skill => {
                            const skillTag = document.createElement('span');
                            skillTag.className = 'skill-tag';
                            skillTag.style.cssText = 'background: #e8f4fd; color: #0066cc; padding: 0.3rem 0.8rem; border-radius: 20px; font-size: 0.85rem; font-weight: 500;';
                            skillTag.textContent = skill.trim();
                            modalSkillsContainer.appendChild(skillTag);
                        });
                    }

                    // Open modal
                    portfolioViewModal.classList.add('active');
                    document.body.style.overflow = 'hidden';

                    console.log('Portfolio view opened for:', projectTitle);
                });
            });

            // Fetch and display the hourly_rate from the new API route in the Hourly Rate section
            fetch('/api/genius/hourly_rate')
                .then(response => response.json())
                .then(data => {
                    const el = document.getElementById('hourlyRate');
                    if (data.success && data.hourly_rate !== undefined && data.hourly_rate !== null && data.hourly_rate !== '' && data.hourly_rate != 0) {
                        el.textContent = `$${data.hourly_rate}`;
                    } else {
                        el.textContent = 'No rate set';
                    }
                })
                .catch(() => {
                    document.getElementById('hourlyRate').textContent = 'Unavailable';
                });

        // Portfolio Content Functions
        function addImageUpload() {
            console.log('Image upload button clicked');
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/*';
            input.multiple = true; // Allow multiple file selection
            input.style.display = 'none';

            input.onchange = function(e) {
                const files = e.target.files;
                console.log('Files selected:', files.length, 'images');

                if (files.length > 0) {
                    // Process each selected file
                    Array.from(files).forEach((file, index) => {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            console.log(`Image ${index + 1} loaded, adding content block`);
                            addContentBlock('image', {
                                src: e.target.result,
                                name: file.name,
                                size: (file.size / 1024 / 1024).toFixed(2) + ' MB'
                            });
                        };
                        reader.readAsDataURL(file);
                    });
                }
            };

            document.body.appendChild(input);
            input.click();
            document.body.removeChild(input);
        }

        function addVideoUpload() {
            console.log('Video upload button clicked');
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'video/*';
            input.multiple = true; // Allow multiple video selection
            input.style.display = 'none';

            input.onchange = function(e) {
                const files = e.target.files;
                console.log('Video files selected:', files.length, 'videos');

                if (files.length > 0) {
                    Array.from(files).forEach((file, index) => {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            console.log(`Video ${index + 1} loaded, adding content block`);
                            addContentBlock('video', {
                                src: e.target.result,
                                name: file.name,
                                size: (file.size / 1024 / 1024).toFixed(2) + ' MB'
                            });
                        };
                        reader.readAsDataURL(file);
                    });
                }
            };

            document.body.appendChild(input);
            input.click();
            document.body.removeChild(input);
        }

        function addTextBlock() {
            console.log('Text block button clicked');
            // Create text block modal
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.5); z-index: 10000; display: flex;
                justify-content: center; align-items: center;
            `;

            modal.innerHTML = `
                <div style="background: white; border-radius: 8px; padding: 2rem; max-width: 600px; width: 90%;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                        <h3 style="margin: 0; color: #004AAD;">Add Text Block</h3>
                        <div style="display: flex; gap: 1rem;">
                            <button id="plainTextBtn" style="padding: 0.5rem 1rem; border: 1px solid #004AAD; background: #004AAD; color: white; border-radius: 4px; cursor: pointer;">Plain text</button>
                            <button id="markdownBtn" style="padding: 0.5rem 1rem; border: 1px solid #004AAD; background: white; color: #004AAD; border-radius: 4px; cursor: pointer;">Markdown</button>
                        </div>
                    </div>
                    <div style="margin-bottom: 1rem;">
                        <label style="display: block; font-weight: 600; margin-bottom: 0.5rem;">Heading</label>
                        <input type="text" id="textHeading" placeholder="Enter heading" style="width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 4px;">
                    </div>
                    <div style="margin-bottom: 1rem;">
                        <textarea id="textContent" placeholder="Enter your text" style="width: 100%; height: 200px; padding: 0.75rem; border: 1px solid #ddd; border-radius: 4px; resize: vertical;"></textarea>
                    </div>
                    <div style="display: flex; justify-content: flex-end; gap: 1rem;">
                        <button id="cancelTextBtn" style="padding: 0.75rem 1.5rem; border: 1px solid #ddd; background: white; color: #666; border-radius: 4px; cursor: pointer;">Cancel</button>
                        <button id="addTextBtn" style="padding: 0.75rem 1.5rem; border: none; background: #CD208B; color: white; border-radius: 4px; cursor: pointer;">Add Text</button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            console.log('Text block modal created and added to DOM');

            // Event listeners
            modal.querySelector('#cancelTextBtn').onclick = () => {
                console.log('Text block cancelled');
                document.body.removeChild(modal);
            };
            modal.querySelector('#addTextBtn').onclick = () => {
                const heading = modal.querySelector('#textHeading').value;
                const content = modal.querySelector('#textContent').value;
                console.log('Adding text block:', { heading, content });
                if (content.trim()) {
                    addContentBlock('text', { heading, content });
                    document.body.removeChild(modal);
                } else {
                    alert('Please enter some text content');
                }
            };
        }

        function addPdfUpload() {
            console.log('PDF upload button clicked');
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.pdf';
            input.style.display = 'none';

            input.onchange = function(e) {
                console.log('PDF file selected:', e.target.files[0]);
                const file = e.target.files[0];
                if (file) {
                    console.log('Adding PDF content block');
                    addContentBlock('pdf', {
                        name: file.name,
                        size: (file.size / 1024 / 1024).toFixed(2) + ' MB'
                    });
                }
            };

            document.body.appendChild(input);
            input.click();
            document.body.removeChild(input);
        }

        function addAudio() {
            console.log('Audio upload button clicked');
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'audio/*';
            input.multiple = true; // Allow multiple audio selection
            input.style.display = 'none';

            input.onchange = function(e) {
                const files = e.target.files;
                console.log('Audio files selected:', files.length, 'audio files');

                if (files.length > 0) {
                    Array.from(files).forEach((file, index) => {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            console.log(`Audio ${index + 1} loaded, adding content block`);
                            addContentBlock('audio', {
                                src: e.target.result,
                                name: file.name,
                                size: (file.size / 1024 / 1024).toFixed(2) + ' MB'
                            });
                        };
                        reader.readAsDataURL(file);
                    });
                }
            };

            document.body.appendChild(input);
            input.click();
            document.body.removeChild(input);
        }

        function addLink() {
            console.log('Add link button clicked');
            // Create link modal
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.5); z-index: 10000; display: flex;
                justify-content: center; align-items: center;
            `;

            modal.innerHTML = `
                <div style="background: white; border-radius: 8px; padding: 2rem; max-width: 500px; width: 90%;">
                    <div style="margin-bottom: 1rem;">
                        <h3 style="margin: 0; color: #004AAD;">Add Link</h3>
                    </div>
                    <div style="margin-bottom: 1rem;">
                        <label style="display: block; font-weight: 600; margin-bottom: 0.5rem;">Link Title</label>
                        <input type="text" id="linkTitle" placeholder="Enter link title" style="width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 4px;">
                    </div>
                    <div style="margin-bottom: 1rem;">
                        <label style="display: block; font-weight: 600; margin-bottom: 0.5rem;">URL</label>
                        <input type="url" id="linkUrl" placeholder="https://example.com" style="width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 4px;">
                    </div>
                    <div style="display: flex; justify-content: flex-end; gap: 1rem;">
                        <button id="cancelLinkBtn" style="padding: 0.75rem 1.5rem; border: 1px solid #ddd; background: white; color: #666; border-radius: 4px; cursor: pointer;">Cancel</button>
                        <button id="addLinkBtn" style="padding: 0.75rem 1.5rem; border: none; background: #CD208B; color: white; border-radius: 4px; cursor: pointer;">Add Link</button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            console.log('Link modal created and added to DOM');

            // Event listeners
            modal.querySelector('#cancelLinkBtn').onclick = () => {
                console.log('Link cancelled');
                document.body.removeChild(modal);
            };
            modal.querySelector('#addLinkBtn').onclick = () => {
                const title = modal.querySelector('#linkTitle').value;
                const url = modal.querySelector('#linkUrl').value;
                console.log('Adding link:', { title, url });
                if (title.trim() && url.trim()) {
                    addContentBlock('link', { title, url });
                    document.body.removeChild(modal);
                } else {
                    alert('Please enter both title and URL');
                }
            };
        }

        function addContentBlock(type, data) {
            const container = document.getElementById('portfolioContent');
            const blockId = 'block_' + Date.now();

            let blockHTML = '';

            switch(type) {
                case 'image':
                    blockHTML = `
                        <div class="content-block" id="${blockId}" style="margin-bottom: 1rem; padding: 1rem; border: 1px solid #ddd; border-radius: 4px; position: relative;">
                            <button onclick="removeBlock('${blockId}')" style="position: absolute; top: 5px; right: 5px; background: #ff4444; color: white; border: none; border-radius: 50%; width: 20px; height: 20px; cursor: pointer; font-size: 12px;">×</button>
                            <img src="${data.src}" alt="${data.name}" style="max-width: 100%; height: auto; border-radius: 4px; cursor: pointer;" onclick="openImagePreview('${data.src}', '${data.name}')">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 0.5rem;">
                                <p style="margin: 0; font-size: 0.9rem; color: #666; flex: 1;">${data.name}</p>
                                ${data.size ? `<span style="font-size: 0.8rem; color: #999;">${data.size}</span>` : ''}
                            </div>
                        </div>
                    `;
                    break;
                case 'video':
                    blockHTML = `
                        <div class="content-block" id="${blockId}" style="margin-bottom: 1rem; padding: 1rem; border: 1px solid #ddd; border-radius: 4px; position: relative;">
                            <button onclick="removeBlock('${blockId}')" style="position: absolute; top: 5px; right: 5px; background: #ff4444; color: white; border: none; border-radius: 50%; width: 20px; height: 20px; cursor: pointer; font-size: 12px;">×</button>
                            <video controls style="max-width: 100%; height: auto; border-radius: 4px;">
                                <source src="${data.src}" type="video/mp4">
                                Your browser does not support the video tag.
                            </video>
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 0.5rem;">
                                <p style="margin: 0; font-size: 0.9rem; color: #666; flex: 1;">${data.name}</p>
                                ${data.size ? `<span style="font-size: 0.8rem; color: #999;">${data.size}</span>` : ''}
                            </div>
                        </div>
                    `;
                    break;
                case 'text':
                    blockHTML = `
                        <div class="content-block" id="${blockId}" style="margin-bottom: 1rem; padding: 1rem; border: 1px solid #ddd; border-radius: 4px; position: relative;">
                            <button onclick="removeBlock('${blockId}')" style="position: absolute; top: 5px; right: 5px; background: #ff4444; color: white; border: none; border-radius: 50%; width: 20px; height: 20px; cursor: pointer; font-size: 12px;">×</button>
                            ${data.heading ? `<h4 style="margin: 0 0 0.5rem 0; color: #004AAD;">${data.heading}</h4>` : ''}
                            <p style="margin: 0; line-height: 1.6; white-space: pre-wrap;">${data.content}</p>
                        </div>
                    `;
                    break;
                case 'pdf':
                    blockHTML = `
                        <div class="content-block" id="${blockId}" style="margin-bottom: 1rem; padding: 1rem; border: 1px solid #ddd; border-radius: 4px; position: relative; display: flex; align-items: center; gap: 1rem;">
                            <button onclick="removeBlock('${blockId}')" style="position: absolute; top: 5px; right: 5px; background: #ff4444; color: white; border: none; border-radius: 50%; width: 20px; height: 20px; cursor: pointer; font-size: 12px;">×</button>
                            <i class="fas fa-file-pdf" style="font-size: 2rem; color: #ff4444;"></i>
                            <div>
                                <p style="margin: 0; font-weight: 600;">${data.name}</p>
                                <p style="margin: 0; font-size: 0.9rem; color: #666;">${data.size}</p>
                            </div>
                        </div>
                    `;
                    break;
                case 'audio':
                    blockHTML = `
                        <div class="content-block" id="${blockId}" style="margin-bottom: 1rem; padding: 1rem; border: 1px solid #ddd; border-radius: 4px; position: relative;">
                            <button onclick="removeBlock('${blockId}')" style="position: absolute; top: 5px; right: 5px; background: #ff4444; color: white; border: none; border-radius: 50%; width: 20px; height: 20px; cursor: pointer; font-size: 12px;">×</button>
                            <audio controls style="width: 100%;">
                                <source src="${data.src}" type="audio/mpeg">
                                Your browser does not support the audio element.
                            </audio>
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 0.5rem;">
                                <p style="margin: 0; font-size: 0.9rem; color: #666; flex: 1;">${data.name}</p>
                                ${data.size ? `<span style="font-size: 0.8rem; color: #999;">${data.size}</span>` : ''}
                            </div>
                        </div>
                    `;
                    break;
                case 'link':
                    blockHTML = `
                        <div class="content-block" id="${blockId}" style="margin-bottom: 1rem; padding: 1rem; border: 1px solid #ddd; border-radius: 4px; position: relative; display: flex; align-items: center; gap: 1rem;">
                            <button onclick="removeBlock('${blockId}')" style="position: absolute; top: 5px; right: 5px; background: #ff4444; color: white; border: none; border-radius: 50%; width: 20px; height: 20px; cursor: pointer; font-size: 12px;">×</button>
                            <i class="fas fa-link" style="font-size: 1.5rem; color: #004AAD;"></i>
                            <div style="flex: 1;">
                                <a href="${data.url}" target="_blank" style="color: #004AAD; text-decoration: none; font-weight: 600; display: block;">${data.title}</a>
                                <p style="margin: 0; font-size: 0.9rem; color: #666; word-break: break-all;">${data.url}</p>
                            </div>
                        </div>
                    `;
                    break;
            }

            container.insertAdjacentHTML('beforeend', blockHTML);
        }

        function removeBlock(blockId) {
            const block = document.getElementById(blockId);
            if (block) {
                block.remove();
            }
        }

        function openImagePreview(src, name) {
            console.log('Opening image preview for:', name);
            // Create image preview modal
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.8); z-index: 10000; display: flex;
                justify-content: center; align-items: center; cursor: pointer;
            `;

            modal.innerHTML = `
                <div style="max-width: 90%; max-height: 90%; position: relative;">
                    <button onclick="this.parentElement.parentElement.remove()" style="position: absolute; top: -40px; right: 0; background: white; color: #333; border: none; border-radius: 50%; width: 30px; height: 30px; cursor: pointer; font-size: 16px;">×</button>
                    <img src="${src}" alt="${name}" style="max-width: 100%; max-height: 100%; border-radius: 8px; box-shadow: 0 4px 20px rgba(0,0,0,0.3);">
                    <p style="color: white; text-align: center; margin-top: 1rem; font-size: 0.9rem;">${name}</p>
                </div>
            `;

            // Close modal when clicking outside the image
            modal.onclick = function(e) {
                if (e.target === modal) {
                    modal.remove();
                }
            };

            document.body.appendChild(modal);
        }

        // Setup portfolio button event listeners
        function setupPortfolioButtons() {
            console.log('Setting up portfolio button listeners');
            const imageBtn = document.getElementById('uploadImageBtn');
            const videoBtn = document.getElementById('uploadVideoBtn');
            const textBtn = document.getElementById('addTextBlockBtn');
            const linkBtn = document.getElementById('addLinkBtn');
            const pdfBtn = document.getElementById('uploadPdfBtn');
            const audioBtn = document.getElementById('addAudioBtn');

            console.log('Image button found:', imageBtn);
            console.log('Video button found:', videoBtn);
            console.log('Text button found:', textBtn);
            console.log('Link button found:', linkBtn);
            console.log('PDF button found:', pdfBtn);
            console.log('Audio button found:', audioBtn);

            if (imageBtn) {
                imageBtn.removeEventListener('click', addImageUpload); // Remove existing listener
                imageBtn.addEventListener('click', addImageUpload);
            }
            if (videoBtn) {
                videoBtn.removeEventListener('click', addVideoUpload);
                videoBtn.addEventListener('click', addVideoUpload);
            }
            if (textBtn) {
                textBtn.removeEventListener('click', addTextBlock);
                textBtn.addEventListener('click', addTextBlock);
            }
            if (linkBtn) {
                linkBtn.removeEventListener('click', addLink);
                linkBtn.addEventListener('click', addLink);
            }
            if (pdfBtn) {
                pdfBtn.removeEventListener('click', addPdfUpload);
                pdfBtn.addEventListener('click', addPdfUpload);
            }
            if (audioBtn) {
                audioBtn.removeEventListener('click', addAudio);
                audioBtn.addEventListener('click', addAudio);
            }
        }

        });
    </script>
</body>
</html>


